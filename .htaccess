# Force HTTPS (Production) - Auto-detects environment
<IfModule mod_rewrite.c>
    RewriteEngine On
    
    # Only force HTTPS on production (not localhost or local IPs)
    RewriteCond %{HTTPS} off
    RewriteCond %{HTTP_HOST} !^localhost [NC]
    RewriteCond %{HTTP_HOST} !^127\.0\.0\.1 [NC]
    RewriteCond %{HTTP_HOST} !^192\.168\. [NC]
    RewriteCond %{HTTP_HOST} !^10\. [NC]
    RewriteCond %{HTTP_HOST} !\.local$ [NC]
    RewriteRule (.*) https://%{HTTP_HOST}%{REQUEST_URI} [L,R=301]
</IfModule>

# Enable PHP for multiple versions (compatibility across hosting environments)
<IfModule mod_php7.c>
    php_value short_open_tag 1
</IfModule>
<IfModule mod_php8.c>
    php_value short_open_tag 1
</IfModule>

# Prevent directory listing
Options -Indexes

# Set default character set
AddDefaultCharset UTF-8

# Set directory index files
DirectoryIndex index.php index.html index.htm

# ============================================================================
# COMPREHENSIVE URL REWRITING SYSTEM
# Removes .php extensions from all public-facing pages
# Compatible with XAMPP, cPanel, Hostinger, and most hosting environments
# ============================================================================

<IfModule mod_rewrite.c>
    RewriteEngine On
    
    # ========================================================================
    # SECURITY: Block access to sensitive files and directories
    # ========================================================================
    
    # Block access to configuration files
    RewriteRule ^(includes/config\.php|includes/db\.php|composer\.(json|lock)|\.env.*) - [F,L]
    
    # Block access to backup and temporary files
    RewriteRule \.(backup|tmp|log|bak)$ - [F,L]
    
    # Block access to development files
    RewriteRule ^(\.|vendor/|node_modules/|\.git/|\.vscode/|\.cursor/) - [F,L]
    
    # ========================================================================
    # BYPASS RULES: Don't rewrite these paths
    # ========================================================================
    
    # Skip rewriting for existing files and directories
    RewriteCond %{REQUEST_FILENAME} -f [OR]
    RewriteCond %{REQUEST_FILENAME} -d
    RewriteRule ^ - [L]
    
    # Skip rewriting for administrative and API endpoints
    RewriteRule ^(admin|api|vendor|assets|includes|cache|data/.*\.json)($|/) - [L]
    
    # Skip rewriting for handlers and specific utility files
    RewriteRule ^(.*-handler\.php|download_flags\.php|test-.*\.php|api\.php|.*save-.*\.php)$ - [L]
    
    # ========================================================================
    # PHP EXTENSION REMOVAL: Main rewriting rules
    # ========================================================================
    
    # Handle subdirectories with clean URLs
    # Pattern: /subdirectory/page -> /subdirectory/page.php
    RewriteCond %{REQUEST_FILENAME} !-f
    RewriteCond %{REQUEST_FILENAME} !-d
    RewriteRule ^(world|notc|live|reportmodule)/([^/]+(?:-[^/]+)*)/?$ $1/$2.php [QSA,L]
    
    # Handle root level pages with clean URLs (including compound names)
    # Pattern: /page-name -> /page-name.php
    RewriteCond %{REQUEST_FILENAME} !-f
    RewriteCond %{REQUEST_FILENAME} !-d
    RewriteRule ^([^./]+(?:-[^./]+)*)/?$ $1.php [QSA,L]
    
    # ========================================================================
    # REDIRECT .php URLs to clean URLs (SEO and user experience)
    # ========================================================================
    
    # Redirect root level .php files to clean URLs (except handlers and admin)
    RewriteCond %{ENV:REDIRECT_STATUS} ^$
    RewriteCond $1 !^(admin|api|includes|vendor|cache|data) [NC]
    RewriteCond $1 !.*-handler$ [NC]
    RewriteCond $1 !^(download_flags|test-.*|api)$ [NC]
    RewriteRule ^([^/]+)\.php$ $1? [R=301,QSA,L]
    
    # Redirect subdirectory .php URLs to clean URLs
    RewriteCond %{ENV:REDIRECT_STATUS} ^$
    RewriteCond $2 !.*-handler$ [NC]
    RewriteCond $2 !.*-api$ [NC]
    RewriteCond $2 !.*save-.* [NC]
    RewriteRule ^(world|notc|live|reportmodule)/([^/]+)\.php$ $1/$2? [R=301,QSA,L]
    
    # ========================================================================
    # LANGUAGE DETECTION AND HANDLING
    # ========================================================================
    
    # Don't rewrite if lang parameter already exists
    RewriteCond %{QUERY_STRING} (?:^|&)lang= [NC]
    RewriteRule ^ - [L]
    
    # Append lang parameter based on cookie if it exists
    RewriteCond %{HTTP_COOKIE} lang=([^;]+) [NC]
    RewriteCond %{QUERY_STRING} !(?:^|&)lang= [NC]
    RewriteRule ^(.*)$ $1?lang=%1 [QSA,L]
    
    # ========================================================================
    # ERROR HANDLING: Fallback for missing pages
    # ========================================================================
    
    # Custom 404 handling for clean URLs that don't exist
    RewriteCond %{REQUEST_FILENAME} !-f
    RewriteCond %{REQUEST_FILENAME} !-d
    RewriteCond %{REQUEST_URI} !^/404
    RewriteCond %{REQUEST_URI} !\.(css|js|png|jpg|jpeg|gif|webp|svg|ico|pdf|zip)$ [NC]
    RewriteRule ^([^./]+)/?$ index.php [QSA,L]
    
</IfModule>

# Compress text files for faster transmission
<IfModule mod_deflate.c>
    AddOutputFilterByType DEFLATE text/html text/plain text/xml text/css text/javascript application/javascript application/x-javascript application/json application/xml
</IfModule>

# Enable browser caching
<IfModule mod_expires.c>
    ExpiresActive On
    ExpiresByType image/jpg "access plus 1 year"
    ExpiresByType image/jpeg "access plus 1 year"
    ExpiresByType image/gif "access plus 1 year"
    ExpiresByType image/png "access plus 1 year"
    ExpiresByType image/webp "access plus 1 year"
    ExpiresByType image/svg+xml "access plus 1 year"
    ExpiresByType text/css "access plus 1 month"
    ExpiresByType application/pdf "access plus 1 month"
    ExpiresByType application/javascript "access plus 1 month"
    ExpiresByType text/javascript "access plus 1 month"
    ExpiresByType text/x-javascript "access plus 1 month"
    ExpiresByType application/x-shockwave-flash "access plus 1 month"
    ExpiresByType image/x-icon "access plus 1 year"
    ExpiresDefault "access plus 2 days"
</IfModule>

# Set security headers
<IfModule mod_headers.c>
    Header always set X-XSS-Protection "1; mode=block"
    Header always set X-Content-Type-Options "nosniff"
    Header always set X-Frame-Options "SAMEORIGIN"
    Header always set Referrer-Policy "strict-origin-when-cross-origin"
</IfModule>

# PHP settings - Compatible with multiple PHP versions
<IfModule mod_php7.c>
    # Set max upload size
    php_value upload_max_filesize 10M
    php_value post_max_size 20M
    php_value max_execution_time 300
    php_value max_input_time 300
    
    # Set error reporting (auto-detects environment)
    php_flag display_errors Off
    php_flag log_errors On
</IfModule>

<IfModule mod_php8.c>
    # Set max upload size
    php_value upload_max_filesize 10M
    php_value post_max_size 20M
    php_value max_execution_time 300
    php_value max_input_time 300
    
    # Set error reporting (auto-detects environment)
    php_flag display_errors Off
    php_flag log_errors On
</IfModule>

# ============================================================================
# PRODUCTION OPTIMIZATIONS
# ============================================================================

# Prevent access to sensitive files in production
<FilesMatch "\.(htaccess|htpasswd|ini|log|sh|inc|backup|bak|conf)$">
    Order Allow,Deny
    Deny from all
</FilesMatch>

# Performance: ETags for better caching
FileETag MTime Size

# Performance: Enable Keep-Alive
<IfModule mod_headers.c>
    Header set Connection keep-alive
</IfModule> 