{"cities": [{"name": "Belo Horizonte", "country": "Brazil", "countryCode": "BR", "admin1": "Minas Gerais", "population": 2721564, "latitude": "-19.92083", "longitude": "-43.93778", "geonameId": 3470127, "timezone": "America/Sao_Paulo"}, {"name": "Kingston", "country": "Jamaica", "countryCode": "JM", "admin1": "Kingston", "population": 937700, "latitude": "17.99702", "longitude": "-76.79358", "geonameId": 3489854, "timezone": "America/Jamaica"}, {"name": "Contagem", "country": "Brazil", "countryCode": "BR", "admin1": "Minas Gerais", "population": 627123, "latitude": "-19.93167", "longitude": "-44.05361", "geonameId": 3465624, "timezone": "America/Sao_Paulo"}, {"name": "<PERSON><PERSON>", "country": "Brazil", "countryCode": "BR", "admin1": "Minas Gerais", "population": 573285, "latitude": "-21.76417", "longitude": "-43.35028", "geonameId": 3459505, "timezone": "America/Sao_Paulo"}, {"name": "Uberlândia", "country": "Brazil", "countryCode": "BR", "admin1": "Minas Gerais", "population": 563536, "latitude": "-18.91861", "longitude": "-48.27722", "geonameId": 3445831, "timezone": "America/Sao_Paulo"}, {"name": "Ribeirão das Neves", "country": "Brazil", "countryCode": "BR", "admin1": "Minas Gerais", "population": 406802, "latitude": "-19.76694", "longitude": "-44.08667", "geonameId": 3451353, "timezone": "America/Sao_Paulo"}, {"name": "<PERSON><PERSON>", "country": "Brazil", "countryCode": "BR", "admin1": "Minas Gerais", "population": 384000, "latitude": "-19.96778", "longitude": "-44.19833", "geonameId": 3470044, "timezone": "America/Sao_Paulo"}, {"name": "Montes Claros", "country": "Brazil", "countryCode": "BR", "admin1": "Minas Gerais", "population": 332379, "latitude": "-16.735", "longitude": "-43.86167", "geonameId": 3456814, "timezone": "America/Sao_Paulo"}], "query": "<PERSON>", "total": 8, "source": "geonames_api"}