<?php
require_once '../includes/config.php';
require_once '../includes/languages.php';
require_once '../includes/TranslationService.php';

// Initialize translation service
$translationService = TranslationService::getInstance();
$currentLanguage = Language::getCurrentLanguage();

// Get zone parameter from URL
$zoneParam = $_GET['zone'] ?? '';
$networkParam = $_GET['network'] ?? '';

// Determine what type of dashboard this is
$dashboardType = '';
$dashboardTitle = '';
$filterValue = '';

if (!empty($zoneParam)) {
    $dashboardType = 'zone';
    $dashboardTitle = urldecode($zoneParam);
    $filterValue = $dashboardTitle;
} elseif (!empty($networkParam)) {
    $dashboardType = 'network';
    $dashboardTitle = urldecode($networkParam);
    $filterValue = $dashboardTitle;
} else {
    // Redirect to main dashboard if no parameters
    header('Location: public-dashboard.php');
    exit();
}

// Load registrations data
$dataFile = __DIR__ . '/data.json';
$registrationsData = [];
if (file_exists($dataFile)) {
    $jsonData = file_get_contents($dataFile);
    $data = json_decode($jsonData, true);
    $registrationsData = $data['registrations'] ?? [];
}

// Load saved manual breakdowns for this dashboard (zone or network)
$breakdownStoreFile = __DIR__ . '/zone_breakdowns.json';
$savedBreakdowns = [];
if (file_exists($breakdownStoreFile)) {
    $savedJson = file_get_contents($breakdownStoreFile);
    $savedBreakdowns = json_decode($savedJson, true) ?: [];
}
$breakdownKey = $dashboardType . '|' . $filterValue;
$existingBreakdown = $savedBreakdowns[$breakdownKey] ?? [
    'breakdown' => [],
    'contacts' => [],
    'venues' => []
];

// Fallback: if aggregate store didn't load, try separate per-dashboard file
if (
    empty($existingBreakdown['breakdown']) &&
    empty($existingBreakdown['contacts']) &&
    empty($existingBreakdown['venues'])
) {
    $safe = function (string $s): string {
        $s = trim($s);
        $s = preg_replace('/[^A-Za-z0-9_\-]+/', '_', $s);
        return trim($s, '_');
    };
    $separatePath = __DIR__ . '/zone_breakdowns/' . $safe($dashboardType) . '__' . $safe($filterValue) . '.json';
    if (file_exists($separatePath)) {
        $sep = json_decode(file_get_contents($separatePath), true);
        if (is_array($sep)) {
            $existingBreakdown = $sep;
        }
    }
}


// Build canonical country name index from data files
$canonicalCountryIndex = [];
try {
    $countrySources = [
        __DIR__ . '/../data/countriesbkd.json',
        __DIR__ . '/../data/countries.json',
    ];
    foreach ($countrySources as $countryFile) {
        if (!file_exists($countryFile)) {
            continue;
        }
        $countryJson = json_decode(file_get_contents($countryFile), true);
        if (!is_array($countryJson)) {
            continue;
        }
        $list = $countryJson['countries'] ?? [];
        foreach ($list as $entry) {
            if (isset($entry['name'])) {
                $name = trim((string) $entry['name']);
                if ($name !== '') {
                    $canonicalCountryIndex[strtolower($name)] = $name; // prefer first occurrence
                }
            }
        }
    }
} catch (\Throwable $e) {
    // If building index fails, keep it empty; counting will gracefully yield 0
}

// Filter registrations based on dashboard type
$filteredRegistrations = [];
foreach ($registrationsData as $registration) {
    if ($dashboardType === 'zone') {
        // For zone dashboard, show registrations from that zone
        if (isset($registration['zone']) && $registration['zone'] === $filterValue) {
            $filteredRegistrations[] = $registration;
        }
    } elseif ($dashboardType === 'network') {
        // For network dashboard, show registrations from that network
        if ($registration['type'] === 'network' && isset($registration['network_type']) && $registration['network_type'] === $filterValue) {
            $filteredRegistrations[] = $registration;
        }
    }
}

// Calculate statistics
$totalRegistrations = count($filteredRegistrations);
$totalCrusades = 0;
$totalOnlineCrusades = 0;
$totalOnsiteCrusades = 0;
$totalExpectedAttendance = 0;
$crusadeTypes = [];
$countries = []; // aggregated counts by canonical country name
$cities = [];
$uniqueCoveredCountries = []; // set of canonical country names covered in this dashboard
$onlineTypeSlugs = ['online', 'mystreamspace'];

foreach ($filteredRegistrations as $registration) {
    // Count crusades
    $numCrusades = isset($registration['number_of_crusades']) ? (int) $registration['number_of_crusades'] : 0;
    $totalCrusades += $numCrusades;

    // Count expected attendance (approximate from ranges)
    if (isset($registration['expected_attendance'])) {
        $attendance = $registration['expected_attendance'];
        if (strpos($attendance, '+') !== false) {
            $totalExpectedAttendance += (int)str_replace('+', '', $attendance);
        } elseif (strpos($attendance, '-') !== false) {
            $parts = explode('-', $attendance);
            if (count($parts) >= 2) {
                $totalExpectedAttendance += (int)str_replace(',', '', $parts[1]);
            }
        }
    }

    // Collect crusade types (support array or JSON string) and classify online/onsite
    $hasOnlineType = false;
    $hasAnyType = false;
    if (isset($registration['crusade_types'])) {
        $types = $registration['crusade_types'];
        if (is_string($types)) {
            $decoded = json_decode($types, true);
            if (json_last_error() === JSON_ERROR_NONE && is_array($decoded)) {
                $types = $decoded;
            } else {
                // fallback: treat as single slug/string
                $types = [$types];
            }
        }
        if (is_array($types)) {
            $hasAnyType = count($types) > 0;
            foreach ($types as $type) {
                $typeSlug = is_string($type) ? strtolower(trim($type)) : strtolower(trim((string)$type));
                if ($typeSlug !== '') {
                    $crusadeTypes[$typeSlug] = ($crusadeTypes[$typeSlug] ?? 0) + 1;
                    if (in_array($typeSlug, $onlineTypeSlugs, true)) {
                        $hasOnlineType = true;
                    }
                }
            }
        }
    }
    // If any online type present, count all crusades for this registration as online; otherwise onsite.
    // If no types at all, treat as onsite by default.
    if ($hasOnlineType) {
        $totalOnlineCrusades += $numCrusades;
    } else {
        $totalOnsiteCrusades += $numCrusades;
    }

    // Collect countries (normalized to canonical list)
    if (isset($registration['selected_countries'])) {
        $regCountries = explode(',', (string) $registration['selected_countries']);
        foreach ($regCountries as $countryRaw) {
            $countryTrimmed = trim($countryRaw);
            if ($countryTrimmed === '') {
                continue;
            }
            $key = strtolower($countryTrimmed);
            if (isset($canonicalCountryIndex[$key])) {
                $canonical = $canonicalCountryIndex[$key];
                $countries[$canonical] = ($countries[$canonical] ?? 0) + 1;
                $uniqueCoveredCountries[$canonical] = true;
            }
        }
    }

    // Collect cities
    if (isset($registration['selected_cities_data']) && is_array($registration['selected_cities_data'])) {
        foreach ($registration['selected_cities_data'] as $city) {
            if (isset($city['name'])) {
                $cityKey = $city['name'] . ', ' . ($city['country'] ?? '');
                $cities[$cityKey] = ($cities[$cityKey] ?? 0) + 1;
            }
        }
    }
}

// Prepare available crusade types for venue selection
$availableCrusadeTypes = [];
$crusadeTypeLabels = [
    'mega' => 'Mega Crusades (10,000+ people)',
    'tap2read' => 'TAP2read Crusades',
    'youths-aglow' => 'Youths Aglow Crusades',
    'teevolution' => 'Teevolution Crusades (Teens-focused)',
    'social-media' => 'Social Media Crusades',
    'mall' => 'Mall Crusades',
    'school' => 'School Crusades',
    'hospital' => 'Hospital Crusades',
    'prison' => 'Prison Crusades',
    'online' => 'Online Crusades',
    'mystreamspace' => 'MyStreamSpace Crusades',
    'other' => 'Other'
];

foreach ($crusadeTypes as $typeSlug => $count) {
    if (isset($crusadeTypeLabels[$typeSlug])) {
        $availableCrusadeTypes[$typeSlug] = $crusadeTypeLabels[$typeSlug];
    }
}

// If no crusade types found, add some defaults
if (empty($availableCrusadeTypes)) {
    $availableCrusadeTypes = [
        'mega' => 'Mega Crusades (10,000+ people)',
        'social-media' => 'Social Media Crusades',
        'school' => 'School Crusades',
        'other' => 'Other'
    ];
}

include 'includes/header.php';
?>

<!-- Fixed Video Background -->
<div class="fixed inset-0 w-full h-full z-0">
    <video
        autoplay
        muted
        loop
        playsinline
        class="absolute inset-0 w-full h-full object-cover"
        poster="https://rorcloud.org/serve.php?id=269&name=Night_of_thousand_crusades.jpg">
        <source src="https://rorcloud.org/serve.php?id=279&name=output(compress-video-online.com).mp4" type="video/mp4">
        Your browser does not support the video tag.
    </video>
    <!-- Dark Overlay for better text readability -->
    <div class="absolute inset-0 bg-black bg-opacity-80"></div>
    <!-- Additional gradient overlay for better text contrast -->
    <div class="absolute inset-0 bg-gradient-to-b from-black/30 via-transparent to-black/50"></div>
</div>

<!-- Dashboard Content -->
<section class="min-h-screen text-white relative z-10 py-20">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <!-- Header -->
        <div class="text-center mb-12">
            <div class="flex items-center justify-center mb-6">
                <a href="admin-backend-links" class="mr-4 text-accent hover:text-yellow-300 transition-colors">
                    <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"></path>
                    </svg>
                </a>
                <h1 class="text-4xl sm:text-5xl md:text-6xl font-bold drop-shadow-lg">
                    <?php echo htmlspecialchars($dashboardTitle); ?>
                    <?php
                    if ($currentLanguage === 'en') {
                        echo ucfirst($dashboardType) . ' Dashboard';
                    } else {
                        echo $translationService->translateText(ucfirst($dashboardType) . ' Dashboard', 'en', $currentLanguage) ?? ucfirst($dashboardType) . ' Dashboard';
                    }
                    ?>
                </h1>
            </div>
            <p class="text-xl md:text-2xl drop-shadow-lg text-gray-100 max-w-3xl mx-auto">
                <?php
                if ($currentLanguage === 'en') {
                    echo 'Crusade registrations and statistics for ' . htmlspecialchars($dashboardTitle);
                } else {
                    echo $translationService->translateText('Crusade registrations and statistics for', 'en', $currentLanguage) . ' ' . htmlspecialchars($dashboardTitle);
                }
                ?>
            </p>
            <div class="w-20 h-1 bg-accent mx-auto mt-6"></div>
        </div>

        <!-- Action Buttons -->
        <div class="text-center mb-12">
            <div class="flex flex-col sm:flex-row gap-4 justify-center items-center">
                <a href="register-church"
                   class="inline-flex items-center px-6 py-3 bg-accent hover:bg-yellow-300 text-gray-900 font-semibold rounded-xl transition-all duration-300 shadow-lg hover:shadow-xl transform hover:scale-105">
                    <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"></path>
                    </svg>
                    <?php
                    if ($currentLanguage === 'en') {
                        echo 'Register Church/Group/Zone';
                    } else {
                        echo $translationService->translateText('Register Church/Group/Zone', 'en', $currentLanguage) ?? 'Register Church/Group/Zone';
                    }
                    ?>
                </a>
                <a href="register-network"
                   class="inline-flex items-center px-6 py-3 bg-blue-600 hover:bg-blue-700 text-white font-semibold rounded-xl transition-all duration-300 shadow-lg hover:shadow-xl transform hover:scale-105">
                    <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"></path>
                    </svg>
                    <?php
                    if ($currentLanguage === 'en') {
                        echo 'Register Network';
                    } else {
                        echo $translationService->translateText('Register Network', 'en', $currentLanguage) ?? 'Register Network';
                    }
                    ?>
                </a>
            </div>
        </div>

        <!-- Statistics Cards -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-12">
            <!-- Total Registrations -->
            <div class="bg-white/10 backdrop-blur-md rounded-2xl border border-white/20 p-6 shadow-xl">
                <div class="flex items-center">
                    <div class="w-12 h-12 bg-blue-500 rounded-full flex items-center justify-center mr-4">
                        <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"></path>
                        </svg>
                    </div>
                    <div>
                        <p class="text-gray-300 text-sm">
                            <?php
                            if ($currentLanguage === 'en') {
                                echo 'Total Registrations';
                            } else {
                                echo $translationService->translateText('Total Registrations', 'en', $currentLanguage) ?? 'Total Registrations';
                            }
                            ?>
                        </p>
                        <p class="text-3xl font-bold text-white"><?php echo number_format($totalRegistrations); ?></p>
                    </div>
                </div>
            </div>

            <!-- Total Crusades (All) -->
            <div class="bg-white/10 backdrop-blur-md rounded-2xl border border-white/20 p-6 shadow-xl">
                <div class="flex items-center">
                    <div class="w-12 h-12 bg-green-500 rounded-full flex items-center justify-center mr-4">
                        <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"></path>
                        </svg>
                    </div>
                    <div>
                        <p class="text-gray-300 text-sm">
                            <?php
                            if ($currentLanguage === 'en') {
                                echo 'Total Crusades (All)';
                            } else {
                                echo $translationService->translateText('Total Crusades (All)', 'en', $currentLanguage) ?? 'Total Crusades (All)';
                            }
                            ?>
                        </p>
                        <p id="dynamicTotalCrusades" class="text-3xl font-bold text-white"><?php echo number_format($totalCrusades); ?></p>
                        <p id="remainingCrusades" class="text-gray-400 text-xs mt-1">
                            Remaining: <span id="remainingCount"><?php echo number_format($totalCrusades); ?></span>
                        </p>
                    </div>
                </div>
            </div>

            <!-- Online Crusades -->
            <div class="bg-white/10 backdrop-blur-md rounded-2xl border border-white/20 p-6 shadow-xl">
                <div class="flex items-center">
                    <div class="w-12 h-12 bg-blue-500 rounded-full flex items-center justify-center mr-4">
                        <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 10l4.553-2.276A2 2 0 0122 9.528v4.944a2 2 0 01-2.447 1.804L15 14m0 0l-4.553 2.276A2 2 0 018 14.472V9.528a2 2 0 012.447-1.804L15 10zm0 0V6m0 8v4"></path>
                        </svg>
                    </div>
                    <div>
                        <p class="text-gray-300 text-sm">
                            <?php
                            if ($currentLanguage === 'en') {
                                echo 'Online Crusades';
                            } else {
                                echo $translationService->translateText('Online Crusades', 'en', $currentLanguage) ?? 'Online Crusades';
                            }
                            ?>
                        </p>
                        <p class="text-3xl font-bold text-white"><?php echo number_format($totalOnlineCrusades); ?></p>
                    </div>
                </div>
            </div>

            <!-- Onsite Crusades -->
            <div class="bg-white/10 backdrop-blur-md rounded-2xl border border-white/20 p-6 shadow-xl">
                <div class="flex items-center">
                    <div class="w-12 h-12 bg-amber-500 rounded-full flex items-center justify-center mr-4">
                        <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 7l9-4 9 4-9 4-9-4zm0 6l9 4 9-4m-9 4v6"></path>
                        </svg>
                    </div>
                    <div>
                        <p class="text-gray-300 text-sm">
                            <?php
                            if ($currentLanguage === 'en') {
                                echo 'Onsite Crusades';
                            } else {
                                echo $translationService->translateText('Onsite Crusades', 'en', $currentLanguage) ?? 'Onsite Crusades';
                            }
                            ?>
                        </p>
                        <p class="text-3xl font-bold text-white"><?php echo number_format($totalOnsiteCrusades); ?></p>
                    </div>
                </div>
            </div>

            <!-- Expected Attendance -->
            <div class="bg-white/10 backdrop-blur-md rounded-2xl border border-white/20 p-6 shadow-xl">
                <div class="flex items-center">
                    <div class="w-12 h-12 bg-purple-500 rounded-full flex items-center justify-center mr-4">
                        <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z"></path>
                        </svg>
                    </div>
                    <div>
                        <p class="text-gray-300 text-sm">
                            <?php
                            if ($currentLanguage === 'en') {
                                echo 'Expected Attendance';
                            } else {
                                echo $translationService->translateText('Expected Attendance', 'en', $currentLanguage) ?? 'Expected Attendance';
                            }
                            ?>
                        </p>
                        <p class="text-3xl font-bold text-white"><?php echo number_format($totalExpectedAttendance); ?>+</p>
                    </div>
                </div>
            </div>

            <!-- Countries Covered -->
            <div class="bg-white/10 backdrop-blur-md rounded-2xl border border-white/20 p-6 shadow-xl">
                <div class="flex items-center">
                    <div class="w-12 h-12 bg-orange-500 rounded-full flex items-center justify-center mr-4">
                        <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3.055 11H5a2 2 0 012 2v1a2 2 0 002 2 2 2 0 012 2v2.945M8 3.935V5.5A2.5 2.5 0 0010.5 8h.5a2 2 0 012 2 2 2 0 104 0 2 2 0 012-2h1.064M15 20.488V18a2 2 0 012-2h3.064M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                        </svg>
                    </div>
                    <div>
                        <p class="text-gray-300 text-sm">
                            <?php
                            if ($currentLanguage === 'en') {
                        echo 'Countries Covered';
                            } else {
                                echo $translationService->translateText('Countries Covered', 'en', $currentLanguage) ?? 'Countries Covered';
                            }
                            ?>
                        </p>
                        <p class="text-3xl font-bold text-white"><?php echo count($uniqueCoveredCountries); ?></p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Charts and Data Section -->
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-12">
            <!-- Crusade Types Chart -->
            <div class="bg-white/10 backdrop-blur-md rounded-2xl border border-white/20 p-6 shadow-xl">
                <h3 class="text-xl font-bold text-white mb-6 flex items-center">
                    <svg class="w-5 h-5 mr-2 text-accent" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
                    </svg>
                    <?php
                    if ($currentLanguage === 'en') {
                        echo 'Crusade Types';
                    } else {
                        echo $translationService->translateText('Crusade Types', 'en', $currentLanguage) ?? 'Crusade Types';
                    }
                    ?>
                </h3>
                <div class="space-y-3">
                    <?php
                    if (empty($crusadeTypes)) {
                        echo '<p class="text-gray-300 text-center py-4">No crusade types data available</p>';
                    } else {
                        arsort($crusadeTypes);
                        $maxCount = max(array_values($crusadeTypes)) ?: 1;
                        foreach (array_slice($crusadeTypes, 0, 8) as $type => $count):
                            $percentage = ($count / $maxCount) * 100;
                            $displayType = ucwords(str_replace('-', ' ', $type));
                        ?>
                            <div class="flex items-center justify-between">
                                <span class="text-gray-300 text-sm flex-1 mr-4"><?php echo htmlspecialchars($displayType); ?></span>
                                <div class="flex items-center flex-1">
                                    <div class="w-full bg-gray-700 rounded-full h-2 mr-3">
                                        <div class="bg-accent h-2 rounded-full" style="width: <?php echo $percentage; ?>%"></div>
                                    </div>
                                    <span class="text-white font-semibold text-sm w-8"><?php echo $count; ?></span>
                                </div>
                            </div>
                        <?php endforeach;
                    } ?>
                </div>
            </div>

            <!-- Top Countries -->
            <div class="bg-white/10 backdrop-blur-md rounded-2xl border border-white/20 p-6 shadow-xl">
                <h3 class="text-xl font-bold text-white mb-6 flex items-center">
                    <svg class="w-5 h-5 mr-2 text-accent" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3.055 11H5a2 2 0 012 2v1a2 2 0 002 2 2 2 0 012 2v2.945M8 3.935V5.5A2.5 2.5 0 0010.5 8h.5a2 2 0 012 2 2 2 0 104 0 2 2 0 012-2h1.064M15 20.488V18a2 2 0 012-2h3.064M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                    </svg>
                    <?php
                    if ($currentLanguage === 'en') {
                        echo 'Top Countries';
                    } else {
                        echo $translationService->translateText('Top Countries', 'en', $currentLanguage) ?? 'Top Countries';
                    }
                    ?>
                </h3>
                <div class="space-y-3">
                    <?php
                    if (empty($countries)) {
                        echo '<p class="text-gray-300 text-center py-4">No countries data available</p>';
                    } else {
                        arsort($countries);
                        $maxCount = max(array_values($countries)) ?: 1;
                        foreach (array_slice($countries, 0, 8) as $country => $count):
                            $percentage = ($count / $maxCount) * 100;
                            $displayCountry = ucwords(str_replace('-', ' ', $country));
                        ?>
                            <div class="flex items-center justify-between">
                                <span class="text-gray-300 text-sm flex-1 mr-4"><?php echo htmlspecialchars($displayCountry); ?></span>
                                <div class="flex items-center flex-1">
                                    <div class="w-full bg-gray-700 rounded-full h-2 mr-3">
                                        <div class="bg-green-500 h-2 rounded-full" style="width: <?php echo $percentage; ?>%"></div>
                                    </div>
                                    <span class="text-white font-semibold text-sm w-8"><?php echo $count; ?></span>
                                </div>
                            </div>
                        <?php endforeach;
                    } ?>
                </div>
            </div>
        </div>

        <!-- Registrations Table -->
        <div class="bg-white/10 backdrop-blur-md rounded-2xl border border-white/20 p-6 shadow-xl">
            <div class="flex justify-between items-center mb-6">
                <h3 class="text-xl font-bold text-white flex items-center">
                    <svg class="w-5 h-5 mr-2 text-accent" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                    </svg>
                    <?php
                    if ($currentLanguage === 'en') {
                        echo 'All Registrations';
                    } else {
                        echo $translationService->translateText('All Registrations', 'en', $currentLanguage) ?? 'All Registrations';
                    }
                    ?>
                </h3>
                <div class="text-sm text-gray-300">
                    Total: <?php echo count($filteredRegistrations); ?> registrations
                </div>
            </div>

            <?php if (empty($filteredRegistrations)): ?>
                <div class="text-center py-12">
                    <svg class="w-16 h-16 text-gray-400 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                    </svg>
                    <p class="text-gray-300 text-lg">
                        <?php
                        if ($currentLanguage === 'en') {
                            echo 'No registrations found for this ' . $dashboardType;
                        } else {
                            echo $translationService->translateText('No registrations found for this', 'en', $currentLanguage) . ' ' . $dashboardType;
                        }
                        ?>
                    </p>
                </div>
            <?php else: ?>
                <div class="overflow-x-auto">
                    <table class="w-full text-left">
                        <thead>
                            <tr class="border-b border-white/20">
                                <th class="pb-3 text-gray-300 font-semibold">
                                    <?php
                                    if ($currentLanguage === 'en') {
                                        echo 'Organization';
                                    } else {
                                        echo $translationService->translateText('Organization', 'en', $currentLanguage) ?? 'Organization';
                                    }
                                    ?>
                                </th>
                                <th class="pb-3 text-gray-300 font-semibold">
                                    <?php
                                    if ($currentLanguage === 'en') {
                                        echo 'Contact';
                                    } else {
                                        echo $translationService->translateText('Contact', 'en', $currentLanguage) ?? 'Contact';
                                    }
                                    ?>
                                </th>
                                <th class="pb-3 text-gray-300 font-semibold">
                                    <?php
                                    if ($currentLanguage === 'en') {
                                        echo 'Crusades';
                                    } else {
                                        echo $translationService->translateText('Crusades', 'en', $currentLanguage) ?? 'Crusades';
                                    }
                                    ?>
                                </th>
                                 <th class="pb-3 text-gray-300 font-semibold">
                                     <?php
                                     if ($currentLanguage === 'en') {
                                         echo 'Crusade Types';
                                     } else {
                                         echo $translationService->translateText('Crusade Types', 'en', $currentLanguage) ?? 'Crusade Types';
                                     }
                                     ?>
                                 </th>
                                <th class="pb-3 text-gray-300 font-semibold">
                                    <?php
                                    if ($currentLanguage === 'en') {
                                        echo 'Countries';
                                    } else {
                                        echo $translationService->translateText('Countries', 'en', $currentLanguage) ?? 'Countries';
                                    }
                                    ?>
                                </th>
                                 <th class="pb-3 text-gray-300 font-semibold">
                                     <?php
                                     if ($currentLanguage === 'en') {
                                         echo 'Cities';
                                     } else {
                                         echo $translationService->translateText('Cities', 'en', $currentLanguage) ?? 'Cities';
                                     }
                                     ?>
                                 </th>

                                <th class="pb-3 text-gray-300 font-semibold">
                                    <?php
                                    if ($currentLanguage === 'en') {
                                        echo 'Actions';
                                    } else {
                                        echo $translationService->translateText('Actions', 'en', $currentLanguage) ?? 'Actions';
                                    }
                                    ?>
                                </th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php
                            // Sort by date (most recent first)
                            usort($filteredRegistrations, function($a, $b) {
                                return strtotime($b['registration_date'] ?? '0') - strtotime($a['registration_date'] ?? '0');
                            });

                            // Pagination
                            $page = isset($_GET['page']) ? max(1, (int)$_GET['page']) : 1;
                            $perPage = 50; // Show 50 registrations per page
                            $totalPages = ceil(count($filteredRegistrations) / $perPage);
                            $offset = ($page - 1) * $perPage;
                            $paginatedRegistrations = array_slice($filteredRegistrations, $offset, $perPage);

                            foreach ($paginatedRegistrations as $registration):
                                $displayName = $registration['display_name'] ?? 'N/A';
                                $contactName = ($registration['first_name'] ?? '') . ' ' . ($registration['last_name'] ?? '');
                                $crusadeCount = $registration['number_of_crusades'] ?? 0;
                                // Count unique, valid countries for this registration
                                $countriesCount = 0;
                                if (!empty($registration['selected_countries'])) {
                                    $perRegSet = [];
                                    foreach (explode(',', (string) $registration['selected_countries']) as $cRaw) {
                                        $cTrim = trim($cRaw);
                                        if ($cTrim === '') { continue; }
                                        $ckey = strtolower($cTrim);
                                        if (isset($canonicalCountryIndex[$ckey])) {
                                            $perRegSet[$canonicalCountryIndex[$ckey]] = true;
                                        }
                                    }
                                    $countriesCount = count($perRegSet);
                                }
                                 // Derive crusade types display
                                 $crusadeTypesDisplay = 'N/A';
                                 if (isset($registration['crusade_types'])) {
                                     if (is_array($registration['crusade_types'])) {
                                         $crusadeTypesDisplay = implode(', ', $registration['crusade_types']);
                                     } elseif (is_string($registration['crusade_types'])) {
                                         $decodedTypes = json_decode($registration['crusade_types'], true);
                                         if (json_last_error() === JSON_ERROR_NONE && is_array($decodedTypes)) {
                                             $crusadeTypesDisplay = implode(', ', $decodedTypes);
                                         } else {
                                             $crusadeTypesDisplay = $registration['crusade_types'];
                                         }
                                     }
                                 }
                                 if (!empty($registration['other_crusade_types'])) {
                                     $crusadeTypesDisplay = $crusadeTypesDisplay === 'N/A'
                                         ? (string) $registration['other_crusade_types']
                                         : ($crusadeTypesDisplay . ', ' . (string) $registration['other_crusade_types']);
                                 }
                                 // Count unique cities selected for this registration
                                 $citiesCount = 0;
                                 if (isset($registration['selected_cities_data'])) {
                                     $citiesData = $registration['selected_cities_data'];
                                     if (is_string($citiesData)) {
                                         $decodedCities = json_decode($citiesData, true);
                                         if (json_last_error() === JSON_ERROR_NONE && is_array($decodedCities)) {
                                             $citiesData = $decodedCities;
                                         } else {
                                             $citiesData = [];
                                         }
                                     }
                                     if (is_array($citiesData)) {
                                         $citySet = [];
                                         foreach ($citiesData as $city) {
                                             if (is_array($city)) {
                                                 $name = trim((string)($city['name'] ?? ''));
                                                 $country = trim((string)($city['country'] ?? ''));
                                                 if ($name !== '') {
                                                     $citySet[$name . '|' . $country] = true;
                                                 }
                                             }
                                         }
                                         $citiesCount = count($citySet);
                                     }
                                 }
                                $registrationId = $registration['id'] ?? '';
                                $registrationType = $registration['type'] ?? 'church';
                            ?>
                                <tr class="border-b border-white/10 hover:bg-white/5 transition-colors duration-200">
                                    <td class="py-4 text-white"><?php echo htmlspecialchars($displayName); ?></td>
                                    <td class="py-4 text-gray-300"><?php echo htmlspecialchars(trim($contactName)); ?></td>
                                    <td class="py-4 text-white"><?php echo number_format($crusadeCount); ?></td>
                                     <td class="py-4 text-gray-300"><?php echo htmlspecialchars($crusadeTypesDisplay); ?></td>
                                    <td class="py-4 text-gray-300"><?php echo $countriesCount; ?></td>
                                     <td class="py-4 text-gray-300"><?php echo $citiesCount; ?></td>
                                    <td class="py-4">
                                        <div class="flex space-x-2">
                                            <?php if (!empty($registrationId)): ?>
                                                <!-- Generate Update Link Button -->
                                                <button onclick="generateUpdateLink('<?php echo htmlspecialchars($registrationId); ?>', '<?php echo htmlspecialchars($registrationType); ?>')"
                                                        class="inline-flex items-center px-3 py-1.5 bg-blue-500/20 border border-blue-400/30 text-blue-300 text-sm font-medium rounded-lg hover:bg-blue-500/30 hover:text-blue-200 transition-all duration-200">
                                                    <svg class="w-4 h-4 mr-1.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13.828 10.172a4 4 0 00-5.656 0l-4 4a4 4 0 105.656 5.656l1.102-1.101m-.758-4.899a4 4 0 005.656 0l4-4a4 4 0 00-5.656-5.656l-1.1 1.1"></path>
                                                    </svg>
                                                    <?php
                                                    if ($currentLanguage === 'en') {
                                                        echo 'Edit';
                                                    } else {
                                                        echo $translationService->translateText('Edit', 'en', $currentLanguage) ?? 'Edit';
                                                    }
                                                    ?>
                                                </button>

                                                <!-- View Details Button -->
                                                <button onclick="viewDetails('<?php echo htmlspecialchars($registrationId); ?>')"
                                                        class="inline-flex items-center px-3 py-1.5 bg-green-500/20 border border-green-400/30 text-green-300 text-sm font-medium rounded-lg hover:bg-green-500/30 hover:text-green-200 transition-all duration-200">
                                                    <svg class="w-4 h-4 mr-1.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path>
                                                    </svg>
                                                    <?php
                                                    if ($currentLanguage === 'en') {
                                                        echo 'View';
                                                    } else {
                                                        echo $translationService->translateText('View', 'en', $currentLanguage) ?? 'View';
                                                    }
                                                    ?>
                                                </button>
                                            <?php else: ?>
                                                <span class="text-gray-500 text-sm">No ID</span>
                                            <?php endif; ?>
                                        </div>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>

                <!-- Pagination Controls -->
                <?php if ($totalPages > 1): ?>
                    <div class="mt-6 flex justify-center items-center space-x-2">
                        <div class="text-sm text-gray-300 mr-4">
                            Showing <?php echo $offset + 1; ?>-<?php echo min($offset + $perPage, count($filteredRegistrations)); ?> of <?php echo count($filteredRegistrations); ?> registrations
                        </div>

                        <?php if ($page > 1): ?>
                            <a href="?<?php echo http_build_query(array_merge($_GET, ['page' => $page - 1])); ?>"
                               class="px-3 py-2 bg-white/10 border border-white/20 text-white rounded-lg hover:bg-white/20 transition-colors">
                                Previous
                            </a>
                        <?php endif; ?>

                        <?php
                        $startPage = max(1, $page - 2);
                        $endPage = min($totalPages, $page + 2);

                        for ($i = $startPage; $i <= $endPage; $i++):
                        ?>
                            <a href="?<?php echo http_build_query(array_merge($_GET, ['page' => $i])); ?>"
                               class="px-3 py-2 <?php echo $i === $page ? 'bg-accent text-gray-900' : 'bg-white/10 border border-white/20 text-white hover:bg-white/20'; ?> rounded-lg transition-colors">
                                <?php echo $i; ?>
                            </a>
                        <?php endfor; ?>

                        <?php if ($page < $totalPages): ?>
                            <a href="?<?php echo http_build_query(array_merge($_GET, ['page' => $page + 1])); ?>"
                               class="px-3 py-2 bg-white/10 border border-white/20 text-white rounded-lg hover:bg-white/20 transition-colors">
                                Next
                            </a>
                        <?php endif; ?>
                    </div>
                <?php endif; ?>
            <?php endif; ?>
        </div>

        <!-- Manual Breakdown and Contacts/Venues -->
        <div class="bg-white/10 backdrop-blur-md rounded-2xl border border-white/20 p-6 shadow-xl mt-12">
            <div class="flex items-center justify-between mb-6">
                <h3 class="text-xl font-bold text-white flex items-center">
                    <svg class="w-5 h-5 mr-2 text-accent" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                    </svg>
                    Crusade Breakdown and Contacts
                </h3>
                <div class="text-sm text-gray-300">
                    Total Crusades (All): <span id="breakdownTotalDisplay"><?php echo number_format($totalCrusades); ?></span>
                </div>
            </div>

            <!-- Current Breakdown Summary -->
            <?php if (!empty($existingBreakdown['breakdown']) || !empty($existingBreakdown['contacts']) || !empty($existingBreakdown['venues'])): ?>
            <div class="bg-white/5 backdrop-blur-sm rounded-xl p-6 border border-white/10 shadow-lg mb-8">
                <h4 class="text-lg font-bold text-white mb-4 flex items-center">
                    <svg class="w-5 h-5 mr-2 text-green-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                    </svg>
                    Current Breakdown Summary
                </h4>

                <!-- Breakdown Numbers Summary -->
                <?php if (!empty($existingBreakdown['breakdown'])): ?>
                <div class="mb-6">
                    <h5 class="text-md font-semibold text-white mb-3">Crusade Type Breakdown</h5>
                    <div class="overflow-x-auto">
                        <table class="w-full bg-white/10 rounded-lg border border-white/20">
                            <thead>
                                <tr class="bg-white/20">
                                    <th class="px-4 py-3 text-left text-white font-semibold">Crusade Type</th>
                                    <th class="px-4 py-3 text-center text-white font-semibold">Count</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php
                                $totalBreakdown = 0;
                                foreach ($existingBreakdown['breakdown'] as $type => $count):
                                    if ($count > 0):
                                        $totalBreakdown += $count;
                                        $label = ucwords(str_replace('-', ' ', $type));
                                ?>
                                <tr class="border-t border-white/10">
                                    <td class="px-4 py-3 text-gray-300"><?php echo htmlspecialchars($label); ?></td>
                                    <td class="px-4 py-3 text-center text-white font-semibold"><?php echo $count; ?></td>
                                </tr>
                                <?php endif; endforeach; ?>
                                <tr class="border-t-2 border-accent/50 bg-white/10">
                                    <td class="px-4 py-3 text-white font-bold">Total</td>
                                    <td class="px-4 py-3 text-center text-accent font-bold text-lg"><?php echo $totalBreakdown; ?></td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
                <?php endif; ?>

                <!-- Contacts Summary -->
                <?php
                $hasContacts = false;
                foreach ($existingBreakdown['contacts'] ?? [] as $type => $contacts) {
                    if (!empty($contacts)) { $hasContacts = true; break; }
                }
                if ($hasContacts): ?>
                <div class="mb-6">
                    <h5 class="text-md font-semibold text-white mb-3">Contact Persons</h5>
                    <div class="overflow-x-auto">
                        <table class="w-full bg-white/10 rounded-lg border border-white/20">
                            <thead>
                                <tr class="bg-white/20">
                                    <th class="px-4 py-3 text-left text-white font-semibold">Type</th>
                                    <th class="px-4 py-3 text-left text-white font-semibold">Name</th>
                                    <th class="px-4 py-3 text-left text-white font-semibold">Phone</th>
                                    <th class="px-4 py-3 text-left text-white font-semibold">KingsChat</th>
                                    <th class="px-4 py-3 text-left text-white font-semibold">Email</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($existingBreakdown['contacts'] ?? [] as $type => $contacts):
                                    foreach ($contacts as $contact):
                                        $typeLabel = ucwords(str_replace('-', ' ', $type));
                                        $phone = trim(($contact['phone_code'] ?? '') . ' ' . ($contact['phone'] ?? ''));
                                ?>
                                <tr class="border-t border-white/10">
                                    <td class="px-4 py-3 text-accent font-medium"><?php echo htmlspecialchars($typeLabel); ?></td>
                                    <td class="px-4 py-3 text-gray-300"><?php echo htmlspecialchars($contact['name'] ?? ''); ?></td>
                                    <td class="px-4 py-3 text-gray-300"><?php echo htmlspecialchars($phone); ?></td>
                                    <td class="px-4 py-3 text-gray-300"><?php echo htmlspecialchars($contact['kingschat'] ?? ''); ?></td>
                                    <td class="px-4 py-3 text-gray-300"><?php echo htmlspecialchars($contact['email'] ?? ''); ?></td>
                                </tr>
                                <?php
                                if (is_array($venue) && !empty($venue['contacts']) && is_array($venue['contacts'])) {
                                    echo '<tr class="border-t border-white/10 bg-white/5">';
                                    echo '<td colspan="4" class="px-6 py-3">';
                                    echo '<div class="text-gray-200 text-sm font-semibold mb-2">Contact Person(s) for this Venue</div>';
                                    echo '<div class="space-y-1 text-gray-300 text-sm">';
                                    foreach ($venue['contacts'] as $vc) {
                                        if (!is_array($vc)) { continue; }
                                        $vcPhone = trim(((string)($vc['phone_code'] ?? '')) . ' ' . ((string)($vc['phone'] ?? '')));
                                        echo '<div class="flex flex-wrap gap-x-4">';
                                        echo '<span><span class="text-gray-400">Name:</span> ' . htmlspecialchars($vc['name'] ?? '') . '</span>';
                                        if ($vcPhone !== '') { echo '<span><span class="text-gray-400">Phone:</span> ' . htmlspecialchars($vcPhone) . '</span>'; }
                                        if (!empty($vc['kingschat'])) { echo '<span><span class="text-gray-400">KingsChat:</span> ' . htmlspecialchars($vc['kingschat']) . '</span>'; }
                                        if (!empty($vc['email'])) { echo '<span><span class="text-gray-400">Email:</span> ' . htmlspecialchars($vc['email']) . '</span>'; }
                                        echo '</div>';
                                    }
                                    echo '</div>';
                                    echo '</td>';
                                    echo '</tr>';
                                }
                                endforeach; endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                </div>
                <?php endif; ?>

                <!-- Venues Summary -->
                <?php
                $hasVenues = false;
                foreach ($existingBreakdown['venues'] ?? [] as $type => $venues) {
                    if (!empty($venues)) { $hasVenues = true; break; }
                }
                if ($hasVenues): ?>
                <div class="mb-6">
                    <h5 class="text-md font-semibold text-white mb-3">Crusade Venues</h5>
                    <div class="overflow-x-auto">
                        <table class="w-full bg-white/10 rounded-lg border border-white/20">
                            <thead>
                                <tr class="bg-white/20">
                                    <th class="px-4 py-3 text-left text-white font-semibold">Type</th>
                                    <th class="px-4 py-3 text-left text-white font-semibold">Venue Name</th>
                                    <th class="px-4 py-3 text-left text-white font-semibold">Location</th>
                                    <th class="px-4 py-3 text-left text-white font-semibold">Date</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($existingBreakdown['venues'] ?? [] as $type => $venues):
                                    foreach ($venues as $venue):
                                        $typeLabel = ucwords(str_replace('-', ' ', $type));
                                ?>
                                <tr class="border-t border-white/10">
                                    <td class="px-4 py-3 text-accent font-medium"><?php echo htmlspecialchars($typeLabel); ?></td>
                                    <td class="px-4 py-3 text-gray-300"><?php echo htmlspecialchars($venue['venue'] ?? ''); ?></td>
                                    <td class="px-4 py-3 text-gray-300"><?php echo htmlspecialchars($venue['location'] ?? ''); ?></td>
                                    <td class="px-4 py-3 text-gray-300"><?php echo htmlspecialchars($venue['date'] ?? ''); ?></td>
                                </tr>
                                <?php endforeach; endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                </div>
                <?php endif; ?>

                <?php if (isset($existingBreakdown['updated_at'])): ?>
                <div class="text-sm text-gray-400 text-right">
                    Last updated: <?php echo date('M j, Y g:i A', strtotime($existingBreakdown['updated_at'])); ?>
                </div>
                <?php endif; ?>
            </div>
            <?php endif; ?>

            <!-- Edit Breakdown by Crusade Type -->
            <div class="mb-8">
                <h4 class="text-lg font-semibold text-white mb-3 flex items-center">
                    <svg class="w-5 h-5 mr-2 text-accent" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
                    </svg>
                    Edit Breakdown by Crusade Type
                </h4>
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                    <?php
                    $typeList = array_keys($crusadeTypes);
                    sort($typeList);
                    if (empty($typeList)) {
                        echo '<p class="text-gray-300">No crusade types detected from registrations. You can still fill in manually.</p>';
                    }
                    $prefill = $existingBreakdown['breakdown'] ?? [];
                    foreach ($typeList as $typeSlug):
                        $label = ucwords(str_replace('-', ' ', $typeSlug));
                        $val = isset($prefill[$typeSlug]) ? (int)$prefill[$typeSlug] : '';
                    ?>
                        <div>
                            <label class="block text-sm text-gray-300 mb-1"><?php echo htmlspecialchars($label); ?></label>
                            <input type="number" min="0" step="1"
                                   id="bd_<?php echo htmlspecialchars($typeSlug); ?>"
                                   class="breakdown-input w-full px-3 py-2 bg-white/10 border border-white/20 rounded-lg text-white placeholder-gray-400"
                                   placeholder="Enter count" value="<?php echo htmlspecialchars($val); ?>"
                                   oninput="updateBreakdownTotals()">
                        </div>
                    <?php endforeach; ?>
                    <!-- Always show common ones even if not detected -->
                    <?php
                    $commonTypes = ['mega','social-media','mall','school'];
                    foreach ($commonTypes as $common) {
                        if (!in_array($common, $typeList, true)) {
                            $val = isset($prefill[$common]) ? (int)$prefill[$common] : '';
                            $label = ucwords(str_replace('-', ' ', $common));
                            echo '<div>';
                            echo '<label class="block text-sm text-gray-300 mb-1">' . htmlspecialchars($label) . '</label>';
                            echo '<input type="number" min="0" step="1" id="bd_' . htmlspecialchars($common) . '" class="breakdown-input w-full px-3 py-2 bg-white/10 border border-white/20 rounded-lg text-white placeholder-gray-400" placeholder="Enter count" value="' . htmlspecialchars($val) . '" oninput="updateBreakdownTotals()">';
                            echo '</div>';
                        }
                    }
                    ?>
                </div>
            </div>

            

            <!-- Edit Crusade Venues (per type) -->
            <details class="mb-8 group">
                <summary class="cursor-pointer list-none flex items-center justify-between p-3 rounded-lg bg-white/5 border border-white/10 hover:bg-white/10">
                    <span class="text-lg font-semibold text-white flex items-center">
                        <svg class="w-5 h-5 mr-2 text-accent" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
                        </svg>
                        Edit Crusade Venues (Per Type)
                    </span>
                    <svg class="w-5 h-5 text-gray-300 transition-transform group-open:rotate-180" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"/></svg>
                </summary>
                <div class="mt-4">
                <?php
                // Prepare venues by type (backward compatible: legacy array -> mega)
                $rawVenues = $existingBreakdown['venues'] ?? [];
                $venuesByType = [];
                if (is_array($rawVenues)) {
                    $isAssocV = array_keys($rawVenues) !== range(0, count($rawVenues) - 1);
                    if ($isAssocV) {
                        $venuesByType = $rawVenues;
                    } else {
                        $venuesByType['mega'] = $rawVenues;
                    }
                }
                $typesForVenues = $typeList;
                foreach ($commonTypes as $c) { if (!in_array($c, $typesForVenues, true)) { $typesForVenues[] = $c; } }
                if (empty($typesForVenues)) { $typesForVenues = ['mega','social-media','mall','school']; }
                $defaultVenueType = $typesForVenues[0] ?? 'mega';
                ?>
                <div class="flex items-center gap-3 mb-3">
                    <label for="venueTypeSelect" class="text-gray-300 text-sm">Select Crusade Type</label>
                    <select id="venueTypeSelect" class="px-3 py-2 bg-white/10 border border-white/20 rounded-lg text-white" onchange="onVenueTypeChange()">
                        <?php foreach ($typesForVenues as $typeSlug): $label = ucwords(str_replace('-', ' ', $typeSlug)); ?>
                            <option value="<?php echo htmlspecialchars($typeSlug); ?>" <?php echo $typeSlug === $defaultVenueType ? 'selected' : ''; ?>><?php echo htmlspecialchars($label); ?></option>
                        <?php endforeach; ?>
                    </select>
                    <button type="button" onclick="addVenueRow(document.getElementById('venueTypeSelect').value)" class="px-3 py-1.5 bg-white/10 border border-white/20 text-white rounded-lg hover:bg-white/20 text-sm">Add Venue</button>
                </div>
                <?php foreach ($typesForVenues as $typeSlug):
                    $venueRows = $venuesByType[$typeSlug] ?? [];
                    $hiddenClass = $typeSlug === $defaultVenueType ? '' : 'hidden';
                ?>
                <div id="venuesContainer_<?php echo htmlspecialchars($typeSlug); ?>" data-type="<?php echo htmlspecialchars($typeSlug); ?>" class="space-y-3 <?php echo $hiddenClass; ?>">
                    <?php foreach ($venueRows as $v):
                        $vName = htmlspecialchars($v['venue'] ?? '');
                        $vLocation = htmlspecialchars($v['location'] ?? '');
                        $vCrusadeType = htmlspecialchars($v['crusade_type'] ?? '');

                        // Try to parse location as city data if it looks like JSON
                        $cityDataValue = '';
                        $displayLocation = $vLocation;
                        if (!empty($vLocation) && (strpos($vLocation, '{') === 0 || strpos($vLocation, ',') !== false)) {
                            // If it looks like structured data, try to use it
                            if (strpos($vLocation, ',') !== false) {
                                $parts = explode(',', $vLocation, 2);
                                $cityDataValue = json_encode([
                                    'name' => trim($parts[0]),
                                    'country' => isset($parts[1]) ? trim($parts[1]) : '',
                                    'countryCode' => ''
                                ]);
                            }
                        }
                    ?>
                    <div class="venue-row space-y-3">
                        <div class="grid grid-cols-1 md:grid-cols-4 gap-3 items-start">
                            <input type="text" class="venue-name px-3 py-2 bg-white/10 border border-white/20 rounded-lg text-white" placeholder="Venue Address" value="<?php echo $vName; ?>">
                            <div class="relative">
                                <input type="text" class="venue-city-input px-3 py-2 bg-white/10 border border-white/20 rounded-lg text-white w-full"
                                       placeholder="Search for city..."
                                       value="<?php echo $displayLocation; ?>"
                                       autocomplete="off">
                                <div class="venue-city-autocomplete absolute z-50 w-full mt-1 bg-white border border-gray-300 rounded-lg shadow-lg max-h-60 overflow-y-auto hidden">
                                    <div class="venue-city-suggestions p-2">
                                        <!-- Suggestions will be populated here -->
                                    </div>
                                </div>
                                <input type="hidden" class="venue-city-data" value="<?php echo htmlspecialchars($cityDataValue ?: $displayLocation); ?>">
                            </div>
                            <select class="venue-crusade-type px-3 py-2 bg-white/10 border border-white/20 rounded-lg text-white">
                                <option value="">Select Crusade Type</option>
                                <?php foreach ($availableCrusadeTypes as $typeSlug => $typeLabel): ?>
                                    <option value="<?php echo htmlspecialchars($typeSlug); ?>" <?php echo $vCrusadeType === $typeSlug ? 'selected' : ''; ?>>
                                        <?php echo htmlspecialchars($typeLabel); ?>
                                    </option>
                                <?php endforeach; ?>
                            </select>
                            <button type="button" onclick="removeRow(this)" class="px-3 py-2 bg-red-500/10 border border-red-400/30 text-red-300 rounded-lg hover:bg-red-500/20">Remove</button>
                        </div>
                        <div class="venue-contacts space-y-3">
                            <!-- existing contacts for this venue (if we can match) will be appended dynamically in the future enhancement -->
                            <button type="button" onclick="addVenueContactRowFromButton(this)" class="px-3 py-1.5 bg-white/10 border border-white/20 text-white rounded-lg hover:bg-white/20 text-sm">Add Contact for this Venue</button>
                        </div>
                    </div>
                    <?php endforeach; ?>
                </div>
                <?php endforeach; ?>
                </div>
            </details>

            <div class="flex justify-end gap-3">
                <button type="button" onclick="saveBreakdown()" class="px-5 py-2 bg-accent text-gray-900 font-semibold rounded-lg hover:bg-yellow-300">Save Breakdown</button>
            </div>
        </div>

        <!-- Back to Backend Links -->
        <div class="text-center mt-12">
            <a href="admin-backend-links"
               class="inline-flex items-center px-6 py-3 bg-accent hover:bg-yellow-300 text-gray-900 font-semibold rounded-xl transition-all duration-300 shadow-lg hover:shadow-xl">
                <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"></path>
                </svg>
                <?php
                if ($currentLanguage === 'en') {
                    echo 'Back to Backend Links';
                } else {
                    echo $translationService->translateText('Back to Backend Links', 'en', $currentLanguage) ?? 'Back to Backend Links';
                }
                ?>
            </a>
        </div>
    </div>
</section>

<!-- Update Link Modal -->
<div id="updateLinkModal" class="fixed inset-0 bg-black bg-opacity-50 hidden z-50 flex items-center justify-center p-4">
    <div class="bg-white rounded-2xl max-w-md w-full p-6 shadow-2xl">
        <div class="flex items-center justify-between mb-4">
            <h3 class="text-xl font-semibold text-gray-900">Update Link Generated</h3>
            <button onclick="closeUpdateLinkModal()" class="text-gray-400 hover:text-gray-600">
                <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                </svg>
            </button>
        </div>
        <div class="mb-4">
            <label class="block text-sm font-medium text-gray-700 mb-2">Update Link:</label>
            <div class="flex">
                <input type="text" id="updateLinkInput" readonly
                       class="flex-1 px-3 py-2 border border-gray-300 rounded-l-md text-sm bg-gray-50">
                <button onclick="copyUpdateLink()"
                        class="px-4 py-2 bg-blue-600 text-white text-sm font-medium rounded-r-md hover:bg-blue-700">
                    Copy
                </button>
            </div>
        </div>
        <div class="flex justify-end space-x-3">
            <button onclick="closeUpdateLinkModal()"
                    class="px-4 py-2 bg-gray-200 text-gray-700 rounded-md hover:bg-gray-300">
                Close
            </button>
            <button onclick="openUpdateLink()"
                    class="px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700">
                Open Link
            </button>
        </div>
    </div>
</div>

<!-- Registration Details Modal -->
<div id="detailsModal" class="fixed inset-0 bg-black bg-opacity-50 hidden z-50 flex items-center justify-center p-4">
    <div class="bg-white rounded-2xl max-w-4xl w-full max-h-[90vh] overflow-y-auto shadow-2xl">
        <div class="sticky top-0 bg-white border-b border-gray-200 px-6 py-4 flex justify-between items-center rounded-t-2xl">
            <h2 class="text-xl font-semibold text-gray-900">Registration Details</h2>
            <button onclick="closeDetailsModal()" class="text-gray-400 hover:text-gray-600">
                <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                </svg>
            </button>
        </div>
        <div id="detailsContent" class="p-6">
            <!-- Details will be loaded here -->
        </div>
    </div>
</div>

<!-- Add City Modal -->
<div id="addCityModal" class="fixed inset-0 bg-black bg-opacity-50 hidden z-50 flex items-center justify-center p-4">
    <div class="bg-white rounded-2xl max-w-md w-full p-6 shadow-2xl">
        <div class="flex items-center justify-between mb-4">
            <h3 class="text-xl font-semibold text-gray-900">Add New City</h3>
            <button onclick="closeAddCityModal()" class="text-gray-400 hover:text-gray-600">
                <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                </svg>
            </button>
        </div>
        <div class="space-y-4">
            <div>
                <label for="newCityCountry" class="block text-sm font-medium text-gray-700 mb-2">Country</label>
                <select id="newCityCountry" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                    <option value="">Select a country</option>
                </select>
            </div>
            <div>
                <label for="newCityName" class="block text-sm font-medium text-gray-700 mb-2">City Name</label>
                <input type="text" id="newCityName"
                       class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                       placeholder="Enter city name">
            </div>
        </div>
        <div class="flex justify-end space-x-3 mt-6">
            <button onclick="closeAddCityModal()"
                    class="px-4 py-2 bg-gray-200 text-gray-700 rounded-lg hover:bg-gray-300 transition-colors">
                Cancel
            </button>
            <button onclick="confirmAddCity()"
                    class="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors">
                Add City
            </button>
        </div>
    </div>
</div>

<!-- Loading Overlay -->
<div id="loadingOverlay" class="fixed inset-0 bg-black bg-opacity-50 hidden z-50 flex items-center justify-center">
    <div class="bg-white rounded-lg p-6 flex items-center space-x-3">
        <div class="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600"></div>
        <span class="text-gray-700">Loading...</span>
    </div>
</div>

<!-- JavaScript Functions -->
<script>
let currentUpdateLink = '';

// Generate update link for a registration
async function generateUpdateLink(registrationId, registrationType) {
    showLoading();

    try {
        const response = await fetch('generate-update-link.php', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
            },
            body: `registration_id=${encodeURIComponent(registrationId)}&registration_type=${encodeURIComponent(registrationType)}`
        });

        const data = await response.json();

        if (data.success) {
            currentUpdateLink = data.update_link;
            document.getElementById('updateLinkInput').value = data.update_link;
            showUpdateLinkModal();
        } else {
            alert('Error generating update link: ' + (data.error || 'Unknown error'));
        }
    } catch (error) {
        console.error('Error:', error);
        alert('Error generating update link. Please try again.');
    } finally {
        hideLoading();
    }
}

// View registration details
async function viewDetails(registrationId) {
    console.log('Fetching details for registration ID:', registrationId);
    showLoading();

    try {
        const url = `api/get-registration.php?id=${encodeURIComponent(registrationId)}`;
        console.log('Fetching from URL:', url);

        const response = await fetch(url);
        console.log('Response status:', response.status);

        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }

        const data = await response.json();
        console.log('Response data:', data);

        if (data.success) {
            displayRegistrationDetails(data.registration);
            showDetailsModal();
        } else {
            console.error('API error:', data.error);
            alert('Error loading registration details: ' + (data.error || 'Unknown error'));
        }
    } catch (error) {
        console.error('Fetch error:', error);
        alert('Error loading registration details: ' + error.message + '. Please check the console for more details.');
    } finally {
        hideLoading();
    }
}

// Display registration details in modal
function displayRegistrationDetails(registration) {
    const detailsContent = document.getElementById('detailsContent');

    // Helper function to safely get value
    const getValue = (value, fallback = 'N/A') => {
        return value && value !== '' ? value : fallback;
    };

    // Process crusade types
    let crusadeTypes = 'N/A';
    if (registration.crusade_types) {
        if (Array.isArray(registration.crusade_types)) {
            crusadeTypes = registration.crusade_types.join(', ');
        } else if (typeof registration.crusade_types === 'string') {
            try {
                const parsed = JSON.parse(registration.crusade_types);
                crusadeTypes = Array.isArray(parsed) ? parsed.join(', ') : registration.crusade_types;
            } catch (e) {
                crusadeTypes = registration.crusade_types;
            }
        }
    }

    // Process selected countries
    let selectedCountries = 'N/A';
    if (registration.selected_countries) {
        selectedCountries = registration.selected_countries.split(',').map(c => c.trim()).join(', ');
    }

    // Process selected cities
    let selectedCities = 'N/A';
    if (registration.selected_cities_data) {
        try {
            let citiesData = registration.selected_cities_data;
            if (typeof citiesData === 'string') {
                citiesData = JSON.parse(citiesData);
            }
            if (Array.isArray(citiesData) && citiesData.length > 0) {
                selectedCities = citiesData.map(city => `${city.name || 'Unknown'}, ${city.country || 'Unknown'}`).join('; ');
            }
        } catch (e) {
            console.error('Error parsing cities data:', e);
            selectedCities = 'Error parsing cities data';
        }
    }

    // Format contact name
    const contactName = [registration.first_name, registration.last_name].filter(n => n && n.trim()).join(' ') || 'N/A';

    // Format phone
    const phone = [registration.phone_country_code, registration.phone].filter(p => p && p.trim()).join(' ') || 'N/A';

    // Format date
    const registrationDate = registration.created_at
        ? new Date(registration.created_at).toLocaleDateString('en-US', {
            year: 'numeric',
            month: 'long',
            day: 'numeric',
            hour: '2-digit',
            minute: '2-digit'
        })
        : 'N/A';

    detailsContent.innerHTML = `
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div class="space-y-4">
                <h3 class="text-lg font-semibold text-gray-900 border-b pb-2">Basic Information</h3>
                <div><strong>Organization:</strong> ${getValue(registration.display_name)}</div>
                <div><strong>Type:</strong> ${getValue(registration.type)}</div>
                ${registration.zone ? `<div><strong>Zone:</strong> ${registration.zone}</div>` : ''}
                ${registration.organisation_type ? `<div><strong>Network:</strong> ${registration.organisation_type}</div>` : ''}
                ${registration.church_name ? `<div><strong>Church Name:</strong> ${registration.church_name}</div>` : ''}
                ${registration.organization_name ? `<div><strong>Organization Name:</strong> ${registration.organization_name}</div>` : ''}
                <div><strong>Contact:</strong> ${contactName}</div>
                <div><strong>Email:</strong> ${getValue(registration.email)}</div>
                <div><strong>Phone:</strong> ${phone}</div>
                <div><strong>Registration Date:</strong> ${registrationDate}</div>
            </div>

            <div class="space-y-4">
                <h3 class="text-lg font-semibold text-gray-900 border-b pb-2">Crusade Information</h3>
                <div><strong>Number of Crusades:</strong> ${getValue(registration.number_of_crusades)}</div>
                <div><strong>Expected Attendance:</strong> ${getValue(registration.expected_attendance)}</div>
                <div><strong>Crusade Types:</strong> ${crusadeTypes}</div>
                ${registration.other_crusade_types ? `<div><strong>Other Types:</strong> ${registration.other_crusade_types}</div>` : ''}
            </div>

            <div class="md:col-span-2 space-y-4">
                <h3 class="text-lg font-semibold text-gray-900 border-b pb-2">Location Information</h3>
                <div><strong>Selected Countries:</strong> ${selectedCountries}</div>
                <div><strong>Selected Cities:</strong> ${selectedCities}</div>
                ${registration.additional_comments ? `<div class="mt-4"><strong>Additional Comments:</strong><br><div class="mt-2 p-3 bg-gray-50 rounded-md">${registration.additional_comments}</div></div>` : ''}
            </div>
        </div>
    `;
}

// Modal control functions
function showUpdateLinkModal() {
    document.getElementById('updateLinkModal').classList.remove('hidden');
}

function closeUpdateLinkModal() {
    document.getElementById('updateLinkModal').classList.add('hidden');
}

function showDetailsModal() {
    document.getElementById('detailsModal').classList.remove('hidden');
}

function closeDetailsModal() {
    document.getElementById('detailsModal').classList.add('hidden');
}

function showLoading() {
    document.getElementById('loadingOverlay').classList.remove('hidden');
}

function hideLoading() {
    document.getElementById('loadingOverlay').classList.add('hidden');
}

// Dynamic rows for contacts and venues
function addContactRow(typeSlug) {
    const container = document.getElementById(`contactsContainer_${typeSlug}`);
    if (!container) return;
    const row = document.createElement('div');
    row.className = 'grid grid-cols-1 md:grid-cols-6 gap-3 items-start';
    row.innerHTML = `
        <input type="text" class="contact-name px-3 py-2 bg-white/10 border border-white/20 rounded-lg text-white" placeholder="Name">
        <select class="contact-phone-code px-3 py-2 bg-white/10 border border-white/20 rounded-lg text-white">
            <option value="+1">+1</option>
            <option value="+20">+20</option>
            <option value="+27">+27</option>
            <option value="+30">+30</option>
            <option value="+31">+31</option>
            <option value="+32">+32</option>
            <option value="+33">+33</option>
            <option value="+34">+34</option>
            <option value="+39">+39</option>
            <option value="+40">+40</option>
            <option value="+41">+41</option>
            <option value="+44">+44</option>
            <option value="+49">+49</option>
            <option value="+52">+52</option>
            <option value="+55">+55</option>
            <option value="+60">+60</option>
            <option value="+61">+61</option>
            <option value="+62">+62</option>
            <option value="+63">+63</option>
            <option value="+64">+64</option>
            <option value="+65">+65</option>
            <option value="+66">+66</option>
            <option value="+81">+81</option>
            <option value="+82">+82</option>
            <option value="+84">+84</option>
            <option value="+86">+86</option>
            <option value="+90">+90</option>
            <option value="+91">+91</option>
            <option value="+92">+92</option>
            <option value="+94">+94</option>
            <option value="+98">+98</option>
            <option value="+211">+211</option>
            <option value="+212">+212</option>
            <option value="+213">+213</option>
            <option value="+216">+216</option>
            <option value="+218">+218</option>
            <option value="+220">+220</option>
            <option value="+221">+221</option>
            <option value="+222">+222</option>
            <option value="+223">+223</option>
            <option value="+224">+224</option>
            <option value="+225">+225</option>
            <option value="+226">+226</option>
            <option value="+227">+227</option>
            <option value="+228">+228</option>
            <option value="+229">+229</option>
            <option value="+230">+230</option>
            <option value="+231">+231</option>
            <option value="+232">+232</option>
            <option value="+233">+233</option>
            <option value="+234" selected>+234</option>
            <option value="+235">+235</option>
            <option value="+236">+236</option>
            <option value="+237">+237</option>
            <option value="+238">+238</option>
            <option value="+239">+239</option>
            <option value="+240">+240</option>
            <option value="+241">+241</option>
            <option value="+242">+242</option>
            <option value="+243">+243</option>
            <option value="+244">+244</option>
            <option value="+245">+245</option>
            <option value="+248">+248</option>
            <option value="+249">+249</option>
            <option value="+250">+250</option>
            <option value="+251">+251</option>
            <option value="+252">+252</option>
            <option value="+253">+253</option>
            <option value="+254">+254</option>
            <option value="+255">+255</option>
            <option value="+256">+256</option>
            <option value="+257">+257</option>
            <option value="+258">+258</option>
            <option value="+260">+260</option>
            <option value="+261">+261</option>
            <option value="+263">+263</option>
        </select>
        <input type="text" class="contact-phone px-3 py-2 bg-white/10 border border-white/20 rounded-lg text-white" placeholder="Phone">
        <input type="text" class="contact-kingschat px-3 py-2 bg-white/10 border border-white/20 rounded-lg text-white" placeholder="@KingsChat">
        <input type="email" class="contact-email px-3 py-2 bg-white/10 border border-white/20 rounded-lg text-white" placeholder="Email">
        <button type="button" onclick="removeRow(this)" class="px-3 py-2 bg-red-500/10 border border-red-400/30 text-red-300 rounded-lg hover:bg-red-500/20">Remove</button>
    `;
    container.appendChild(row);
}

function removeRow(buttonEl) {
    const row = buttonEl.closest('.grid');
    if (row && row.parentElement) {
        row.parentElement.removeChild(row);
    }
}

function addVenueRow(typeSlug) {
    const container = document.getElementById(`venuesContainer_${typeSlug}`);
    if (!container) return;
    const row = document.createElement('div');
    row.className = 'venue-row space-y-3';
    row.innerHTML = `
        <div class="grid grid-cols-1 md:grid-cols-4 gap-3 items-start">
            <input type="text" class="venue-name px-3 py-2 bg-white/10 border border-white/20 rounded-lg text-white" placeholder="Venue Address">
            <div class="relative">
                <input type="text" class="venue-city-input px-3 py-2 bg-white/10 border border-white/20 rounded-lg text-white w-full"
                       placeholder="Search for city..."
                       autocomplete="off">
                <div class="venue-city-autocomplete absolute z-50 w-full mt-1 bg-white border border-gray-300 rounded-lg shadow-lg max-h-60 overflow-y-auto hidden">
                    <div class="venue-city-suggestions p-2">
                        <!-- Suggestions will be populated here -->
                    </div>
                </div>
                <input type="hidden" class="venue-city-data" value="">
            </div>
            <select class="venue-crusade-type px-3 py-2 bg-white/10 border border-white/20 rounded-lg text-white">
                <option value="">Select Crusade Type</option>
                ${getCrusadeTypeOptions()}
            </select>
            <button type="button" onclick="removeRow(this)" class="px-3 py-2 bg-red-500/10 border border-red-400/30 text-red-300 rounded-lg hover:bg-red-500/20">Remove</button>
        </div>
        <div class="venue-contacts space-y-3">
            <button type="button" onclick="addVenueContactRowFromButton(this)" class="px-3 py-1.5 bg-white/10 border border-white/20 text-white rounded-lg hover:bg-white/20 text-sm">Add Contact for this Venue</button>
        </div>
    `;
    container.appendChild(row);

    // Initialize city autocomplete for the new row
    const cityInput = row.querySelector('.venue-city-input');
    if (cityInput) {
        setupCityAutocomplete(cityInput);
    }
}

// Dynamic breakdown totals calculation
const originalTotalCrusades = <?php echo $totalCrusades; ?>;


// Removed country/city dependent dropdowns to avoid runtime errors; using free text for location and a date input.

function addVenueContactRowFromButton(btn) {
    const row = btn.closest('.venue-row');
    if (!row) return;
    const container = row.querySelector('.venue-contacts');
    if (!container) return;
    const c = document.createElement('div');
    c.className = 'venue-contact-row grid grid-cols-1 md:grid-cols-6 gap-3 items-start';
    c.innerHTML = `
        <input type="text" class="contact-name px-3 py-2 bg-white/10 border border-white/20 rounded-lg text-white" placeholder="Name">
        <input type="text" class="contact-phone-code px-3 py-2 bg-white/10 border border-white/20 rounded-lg text-white" placeholder="Code e.g. +234">
        <input type="text" class="contact-phone px-3 py-2 bg-white/10 border border-white/20 rounded-lg text-white" placeholder="Phone">
        <input type="text" class="contact-kingschat px-3 py-2 bg-white/10 border border-white/20 rounded-lg text-white" placeholder="@KingsChat">
        <input type="email" class="contact-email px-3 py-2 bg-white/10 border border-white/20 rounded-lg text-white" placeholder="Email">
        <button type="button" onclick="removeRow(this)" class="px-3 py-2 bg-red-500/10 border border-red-400/30 text-red-300 rounded-lg hover:bg-red-500/20">Remove</button>
    `;
    container.appendChild(c);
}

function updateBreakdownTotals() {
    const inputs = document.querySelectorAll('.breakdown-input');
    let totalFromBreakdown = 0;
    let hasInvalidInput = false;

    inputs.forEach(input => {
        const val = parseInt(input.value, 10);
        if (!isNaN(val) && val >= 0) {
            totalFromBreakdown += val;
        }

        // Validate against max available
        if (!isNaN(val) && val > originalTotalCrusades) {
            input.value = originalTotalCrusades;
            totalFromBreakdown = originalTotalCrusades;
            hasInvalidInput = true;
        }
    });

    // Prevent total breakdown from exceeding original total
    if (totalFromBreakdown > originalTotalCrusades) {
        // Find the last modified input and reduce it
        const lastInput = document.activeElement;
        if (lastInput && lastInput.classList.contains('breakdown-input')) {
            const excess = totalFromBreakdown - originalTotalCrusades;
            const currentVal = parseInt(lastInput.value, 10) || 0;
            lastInput.value = Math.max(0, currentVal - excess);
        }
        totalFromBreakdown = originalTotalCrusades;
    }

    // Update displays
    const remaining = originalTotalCrusades - totalFromBreakdown;
    document.getElementById('dynamicTotalCrusades').textContent = originalTotalCrusades.toLocaleString();
    document.getElementById('remainingCount').textContent = remaining.toLocaleString();
    document.getElementById('breakdownTotalDisplay').textContent = originalTotalCrusades.toLocaleString();

    // Color coding for remaining count
    const remainingEl = document.getElementById('remainingCount');
    const remainingContainer = document.getElementById('remainingCrusades');
    if (remaining === 0) {
        remainingContainer.className = 'text-green-400 text-xs mt-1';
    } else if (remaining < originalTotalCrusades * 0.1) {
        remainingContainer.className = 'text-yellow-400 text-xs mt-1';
    } else {
        remainingContainer.className = 'text-gray-400 text-xs mt-1';
    }
}

// Initialize on page load
document.addEventListener('DOMContentLoaded', function() {
    updateBreakdownTotals();

    // Initialize city autocomplete for all venue city inputs
    initializeCityAutocomplete();

    // Force refresh of venue containers to ensure proper display
    setTimeout(() => {
        const activeVenueType = document.getElementById('venueTypeSelect')?.value;
        if (activeVenueType) {
            onVenueTypeChange();
        }
    }, 100);
});

// City autocomplete functionality
let countriesData = [];
let currentCityInputElement = null;
let currentCityHiddenInput = null;

// Available crusade types from PHP
const availableCrusadeTypes = <?php echo json_encode($availableCrusadeTypes); ?>;

// Generate crusade type options HTML
function getCrusadeTypeOptions() {
    let options = '';
    for (const [slug, label] of Object.entries(availableCrusadeTypes)) {
        options += `<option value="${slug}">${label}</option>`;
    }
    return options;
}

// Setup autocomplete for a single city input
function setupCityAutocomplete(input) {
    const container = input.parentElement;
    const autocompleteDiv = container.querySelector('.venue-city-autocomplete');
    const suggestionsDiv = container.querySelector('.venue-city-suggestions');
    const hiddenInput = container.querySelector('.venue-city-data');

    let currentFocus = -1;
    let searchTimeout = null;

    // Search cities via API
    async function searchCities(query) {
        if (query.length < 2) {
            hideAutocomplete();
            return;
        }

        try {
            const response = await fetch(`/crusades/api/cities-search.php?q=${encodeURIComponent(query)}&maxRows=8`);
            const data = await response.json();

            if (data.cities && data.cities.length > 0) {
                displaySuggestions(data.cities);
            } else {
                showNoResults();
            }
        } catch (error) {
            console.error('Error searching cities:', error);
            showNoResults();
        }
    }

    // Display city suggestions
    function displaySuggestions(cities) {
        suggestionsDiv.innerHTML = '';
        currentFocus = -1;

        // Add header
        const headerDiv = document.createElement('div');
        headerDiv.className = 'px-3 py-2 text-xs font-medium text-gray-500 bg-gray-50 border-b border-gray-200';
        headerDiv.innerHTML = 'Select a city from the suggestions below:';
        suggestionsDiv.appendChild(headerDiv);

        cities.forEach((city, index) => {
            const suggestionDiv = document.createElement('div');
            suggestionDiv.className = 'city-suggestion px-3 py-2 hover:bg-green-50 cursor-pointer text-sm border-b border-gray-100 last:border-b-0 transition-colors duration-150';
            suggestionDiv.innerHTML = `
                <div class="flex items-center justify-between">
                    <div>
                        <div class="font-medium text-gray-900">${city.name}</div>
                        <div class="text-gray-600 text-xs">${city.admin1 ? city.admin1 + ', ' : ''}${city.country}</div>
                    </div>
                    <div class="text-green-500 opacity-0 group-hover:opacity-100">
                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                        </svg>
                    </div>
                </div>
            `;

            suggestionDiv.addEventListener('click', () => {
                selectCity(city);
            });

            suggestionDiv.addEventListener('mouseenter', () => {
                currentFocus = index;
                updateFocus();
            });

            suggestionsDiv.appendChild(suggestionDiv);
        });

        showAutocomplete();
    }

    // Select a city
    function selectCity(city) {
        const cityData = {
            name: city.name,
            country: city.country,
            countryCode: city.countryCode,
            admin1: city.admin1 || '',
            latitude: city.latitude || '',
            longitude: city.longitude || ''
        };

        // Update input display
        input.value = `${city.name}, ${city.country}`;

        // Store city data
        hiddenInput.value = JSON.stringify(cityData);

        hideAutocomplete();
    }

    // Show no results message
    function showNoResults() {
        suggestionsDiv.innerHTML = `
            <div class="px-3 py-4 text-center text-gray-500">
                <div class="text-sm mb-2">No cities found</div>
                <div class="text-xs text-gray-400 mb-2">Try another spelling</div>
                <button type="button" class="inline-flex items-center px-3 py-1 bg-green-600 text-white rounded text-xs" onclick="showAddCityModalForInput(this)">
                    Add city
                </button>
            </div>
        `;
        showAutocomplete();
    }

    // Show/hide autocomplete
    function showAutocomplete() {
        autocompleteDiv.classList.remove('hidden');
    }

    function hideAutocomplete() {
        autocompleteDiv.classList.add('hidden');
        currentFocus = -1;
    }

    // Update focus highlighting
    function updateFocus() {
        const suggestions = suggestionsDiv.querySelectorAll('.city-suggestion');
        suggestions.forEach((suggestion, index) => {
            if (index === currentFocus) {
                suggestion.classList.add('bg-gray-100');
            } else {
                suggestion.classList.remove('bg-gray-100');
            }
        });
    }

    // Handle keyboard navigation
    function handleKeyDown(e) {
        const suggestions = suggestionsDiv.querySelectorAll('.city-suggestion');

        if (suggestions.length === 0) return;

        switch (e.key) {
            case 'ArrowDown':
                e.preventDefault();
                currentFocus = currentFocus < suggestions.length - 1 ? currentFocus + 1 : 0;
                updateFocus();
                break;

            case 'ArrowUp':
                e.preventDefault();
                currentFocus = currentFocus > 0 ? currentFocus - 1 : suggestions.length - 1;
                updateFocus();
                break;

            case 'Enter':
                e.preventDefault();
                if (currentFocus >= 0 && suggestions[currentFocus]) {
                    suggestions[currentFocus].click();
                }
                break;

            case 'Escape':
                hideAutocomplete();
                break;
        }
    }

    // Event listeners
    input.addEventListener('input', (e) => {
        const query = e.target.value.trim();

        // Clear previous timeout
        if (searchTimeout) {
            clearTimeout(searchTimeout);
        }

        // Debounce search
        searchTimeout = setTimeout(() => {
            if (query.length >= 2) {
                searchCities(query);
            } else {
                hideAutocomplete();
            }
        }, 300);
    });

    input.addEventListener('keydown', handleKeyDown);

    input.addEventListener('focus', () => {
        const query = input.value.trim();
        if (query.length >= 2) {
            searchCities(query);
        }
    });

    // Hide autocomplete when clicking outside
    document.addEventListener('click', (e) => {
        if (!input.contains(e.target) && !autocompleteDiv.contains(e.target)) {
            hideAutocomplete();
        }
    });
}

// Initialize city autocomplete for all venue city inputs
function initializeCityAutocomplete() {
    document.querySelectorAll('.venue-city-input').forEach(input => {
        setupCityAutocomplete(input);
    });
}

// Show add city modal from input
function showAddCityModalForInput(button) {
    const container = button.closest('.venue-city-autocomplete').parentElement;
    const input = container.querySelector('.venue-city-input');
    const hiddenInput = container.querySelector('.venue-city-data');

    currentCityInputElement = input;
    currentCityHiddenInput = hiddenInput;

    showAddCityModal();
}

// Show add city modal
async function showAddCityModal() {

    // Load countries data if not already loaded
    await loadCountriesData();

    // Populate country dropdown
    const countrySelect = document.getElementById('newCityCountry');
    countrySelect.innerHTML = '<option value="">Select a country</option>';

    countriesData.forEach(country => {
        const option = document.createElement('option');
        option.value = country.code;
        option.textContent = country.name;
        countrySelect.appendChild(option);
    });

    // Clear city name input
    document.getElementById('newCityName').value = '';

    // Show modal
    document.getElementById('addCityModal').classList.remove('hidden');
}

// Close add city modal
function closeAddCityModal() {
    document.getElementById('addCityModal').classList.add('hidden');

    currentCityInputElement = null;
    currentCityHiddenInput = null;
}

// Confirm add city
function confirmAddCity() {
    const countrySelect = document.getElementById('newCityCountry');
    const cityNameInput = document.getElementById('newCityName');

    const countryCode = countrySelect.value;
    const cityName = cityNameInput.value.trim();

    if (!countryCode || !cityName) {
        alert('Please select a country and enter a city name.');
        return;
    }

    // Find country name
    const country = countriesData.find(c => c.code === countryCode);
    if (!country) {
        alert('Invalid country selected.');
        return;
    }

    const cityData = {
        name: cityName,
        country: country.name,
        countryCode: countryCode,
        admin1: '',
        latitude: '',
        longitude: ''
    };

    // Update input display
    if (currentCityInputElement) {
        currentCityInputElement.value = `${cityData.name}, ${cityData.country}`;
    }

    // Store in hidden input
    currentCityHiddenInput.value = JSON.stringify(cityData);

    // Close modal
    closeAddCityModal();
}

// Load countries data
async function loadCountriesData() {
    if (countriesData.length > 0) return countriesData;

    try {
        const response = await fetch('/crusades/data/countries.json');
        const data = await response.json();
        countriesData = data.countries || [];
        return countriesData;
    } catch (error) {
        console.error('Error loading countries data:', error);
        // Fallback to basic countries
        countriesData = [
            { code: 'NG', name: 'Nigeria' },
            { code: 'GH', name: 'Ghana' },
            { code: 'KE', name: 'Kenya' },
            { code: 'ZA', name: 'South Africa' },
            { code: 'US', name: 'United States' },
            { code: 'GB', name: 'United Kingdom' },
            { code: 'CA', name: 'Canada' }
        ];
        return countriesData;
    }
}

// Toggle handlers for dropdown-based forms
function onContactTypeChange() {
    const select = document.getElementById('contactTypeSelect');
    if (!select) return;
    const active = select.value;
    document.querySelectorAll('[id^="contactsContainer_"]').forEach(el => {
        if (el.id === `contactsContainer_${active}`) {
            el.classList.remove('hidden');
        } else {
            el.classList.add('hidden');
        }
    });
}

function onVenueTypeChange() {
    const select = document.getElementById('venueTypeSelect');
    if (!select) return;
    const active = select.value;
    document.querySelectorAll('[id^="venuesContainer_"]').forEach(el => {
        if (el.id === `venuesContainer_${active}`) {
            el.classList.remove('hidden');
        } else {
            el.classList.add('hidden');
        }
    });
}

async function saveBreakdown() {
    showLoading();
    try {
        // Gather breakdown numbers
        const breakdown = {};
        const inputs = document.querySelectorAll('[id^="bd_"]');
        inputs.forEach(input => {
            const slug = input.id.substring(3);
            const val = parseInt(input.value, 10);
            if (!isNaN(val) && val >= 0) {
                breakdown[slug] = val;
            }
        });

        // Contacts per type
        const contacts = {};
        document.querySelectorAll('[id^="contactsContainer_"]').forEach(container => {
            const type = container.dataset.type;
            const rows = container.querySelectorAll('.grid');
            const list = [];
            rows.forEach(row => {
                const nameEl = row.querySelector('.contact-name');
                const codeEl = row.querySelector('.contact-phone-code');
                const phoneEl = row.querySelector('.contact-phone');
                const kcEl = row.querySelector('.contact-kingschat');
                const emailEl = row.querySelector('.contact-email');
                const contact = {
                    name: (nameEl?.value || '').trim(),
                    phone_code: (codeEl?.value || '').trim(),
                    phone: (phoneEl?.value || '').trim(),
                    kingschat: (kcEl?.value || '').trim(),
                    email: (emailEl?.value || '').trim(),
                };
                if (contact.name || contact.phone || contact.email || contact.kingschat) {
                    list.push(contact);
                }
            });
            contacts[type] = list;
        });

        // Venues per type (contacts nested under each venue)
        const venues = {};
        document.querySelectorAll('[id^="venuesContainer_"]').forEach(container => {
            const type = container.dataset.type;
            const rows = container.querySelectorAll('.venue-row');
            const list = [];
            rows.forEach(row => {
                const nameEl = row.querySelector('.venue-name');
                const cityDataEl = row.querySelector('.venue-city-data');
                const crusadeTypeEl = row.querySelector('.venue-crusade-type');
                const venueContactsEl = row.querySelectorAll('.venue-contacts .venue-contact-row');
                const contactsList = [];
                venueContactsEl.forEach(crow => {
                    const name = (crow.querySelector('.contact-name')?.value || '').trim();
                    const phone_code = (crow.querySelector('.contact-phone-code')?.value || '').trim();
                    const phone = (crow.querySelector('.contact-phone')?.value || '').trim();
                    const kingschat = (crow.querySelector('.contact-kingschat')?.value || '').trim();
                    const email = (crow.querySelector('.contact-email')?.value || '').trim();
                    if (name || phone || kingschat || email) {
                        contactsList.push({ name, phone_code, phone, kingschat, email });
                    }
                });
                const venueName = (nameEl?.value || '').trim();
                const cityData = cityDataEl?.value || '';
                const crusadeType = (crusadeTypeEl?.value || '').trim();

                // Parse city data if it exists
                let cityInfo = '';
                if (cityData) {
                    try {
                        const parsed = JSON.parse(cityData);
                        cityInfo = parsed.name + (parsed.country ? ', ' + parsed.country : '');
                    } catch (e) {
                        cityInfo = cityData;
                    }
                }

                const item = {
                    venue: venueName,
                    location: cityInfo,
                    crusade_type: crusadeType
                };
                if (contactsList.length) { item.contacts = contactsList; }
                if (item.venue || item.location || item.crusade_type || contactsList.length) {
                    list.push(item);
                }
            });
            venues[type] = list;
        });

        const payload = {
            dashboard_type: <?php echo json_encode($dashboardType); ?>,
            key: <?php echo json_encode($filterValue); ?>,
            breakdown,
            contacts,
            venues,
        };

        // Send with robust fallback: JSON first (.php then no-ext), then x-www-form-urlencoded
        async function tryPostJson(url) {
            const r = await fetch(url, {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify(payload),
            });
            let data;
            try {
                data = await r.json();
            } catch (e) {
                const txt = await r.text();
                throw new Error(`Unexpected response (${r.status}): ${txt.slice(0, 200)}`);
            }
            if (!r.ok || !data?.success) {
                throw new Error(data?.error || `HTTP ${r.status}`);
            }
            return data;
        }

        async function tryPostForm(url) {
            const r = await fetch(url, {
                method: 'POST',
                headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
                body: `payload=${encodeURIComponent(JSON.stringify(payload))}`,
            });
            let data;
            try {
                data = await r.json();
            } catch (e) {
                const txt = await r.text();
                throw new Error(`Unexpected response (${r.status}): ${txt.slice(0, 200)}`);
            }
            if (!r.ok || !data?.success) {
                throw new Error(data?.error || `HTTP ${r.status}`);
            }
            return data;
        }

        const urls = ['/crusades/notc/save-zone-breakdown.php'];
        let saved = null;
        let lastErr = null;
        for (const u of urls) {
            try {
                saved = await tryPostJson(u);
                break;
            } catch (e1) {
                lastErr = e1;
                try {
                    saved = await tryPostForm(u);
                    break;
                } catch (e2) {
                    lastErr = e2;
                }
            }
        }
        if (!saved) {
            throw lastErr || new Error('Save failed');
        }
        alert('Breakdown saved successfully.');
    } catch (err) {
        console.error(err);
        alert('Failed to save breakdown: ' + err.message);
    } finally {
        hideLoading();
    }
}

// Copy update link to clipboard
function copyUpdateLink() {
    const input = document.getElementById('updateLinkInput');
    input.select();
    input.setSelectionRange(0, 99999);

    try {
        document.execCommand('copy');

        // Show feedback
        const button = event.target;
        const originalText = button.textContent;
        button.textContent = 'Copied!';
        button.classList.add('bg-green-600');
        button.classList.remove('bg-blue-600');

        setTimeout(() => {
            button.textContent = originalText;
            button.classList.remove('bg-green-600');
            button.classList.add('bg-blue-600');
        }, 2000);
    } catch (err) {
        console.error('Failed to copy: ', err);
        alert('Failed to copy link. Please copy manually.');
    }
}

// Open update link in new tab
function openUpdateLink() {
    if (currentUpdateLink) {
        window.open(currentUpdateLink, '_blank');
    }
}

// Close modals when clicking outside
document.addEventListener('click', function(event) {
    const updateModal = document.getElementById('updateLinkModal');
    const detailsModal = document.getElementById('detailsModal');
    const addCityModal = document.getElementById('addCityModal');

    if (event.target === updateModal) {
        closeUpdateLinkModal();
    }

    if (event.target === detailsModal) {
        closeDetailsModal();
    }

    if (event.target === addCityModal) {
        closeAddCityModal();
    }
});

// Close modals with Escape key
document.addEventListener('keydown', function(event) {
    if (event.key === 'Escape') {
        closeUpdateLinkModal();
        closeDetailsModal();
        closeAddCityModal();
    }
});
</script>

<?php include 'includes/footer.php'; ?>