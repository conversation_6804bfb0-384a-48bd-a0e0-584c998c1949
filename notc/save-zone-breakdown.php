<?php
header('Content-Type: application/json');

try {
    // Read raw input and try JSON first
    $raw = file_get_contents('php://input');
    $payload = null;

    // Detect content-type
    $contentType = isset($_SERVER['CONTENT_TYPE']) ? strtolower((string)$_SERVER['CONTENT_TYPE']) : '';
    if ($raw !== false && $raw !== '') {
        $decoded = json_decode($raw, true);
        if (is_array($decoded)) {
            $payload = $decoded;
        }
    }

    // Fallbacks for non-JSON or failed decode
    if (!is_array($payload)) {
        $alt = [];
        // If content-type form-encoded, prefer $_POST
        if (!empty($_POST)) {
            $alt = array_merge($alt, $_POST);
        }
        // Parse raw as querystring (covers text/plain or mis-labeled bodies)
        if (is_string($raw) && trim($raw) !== '') {
            $qsAlt = [];
            parse_str($raw, $qsAlt);
            if (!empty($qsAlt)) {
                $alt = array_merge($alt, $qsAlt);
            }
        }
        // Support payload wrapper
        if (isset($alt['payload'])) {
            $maybe = json_decode((string)$alt['payload'], true);
            if (is_array($maybe)) {
                $payload = $maybe;
            }
        }
        // As a last resort, if expected top-level keys exist directly in $alt, use it
        if (!is_array($payload) && (isset($alt['dashboard_type']) || isset($alt['key']))) {
            // Decode nested JSON strings if present
            foreach (['breakdown','contacts','venues'] as $k) {
                if (isset($alt[$k]) && is_string($alt[$k])) {
                    $try = json_decode((string)$alt[$k], true);
                    if (is_array($try)) {
                        $alt[$k] = $try;
                    }
                }
            }
            $payload = $alt;
        }
    }

    if (!is_array($payload)) {
        // Log bad payload for diagnostics
        $logDir = __DIR__ . '/zone_breakdowns_logs';
        if (!is_dir($logDir)) { @mkdir($logDir, 0755, true); }
        @file_put_contents($logDir . '/failed_' . date('Ymd_His') . '.log',
            "CT: " . $contentType . "\n" . $raw
        );
        throw new Exception('Invalid JSON payload');
    }

    $dashboardType = isset($payload['dashboard_type']) ? (string)$payload['dashboard_type'] : '';
    $key = isset($payload['key']) ? (string)$payload['key'] : '';
    if ($dashboardType === '' || $key === '') {
        throw new Exception('Missing dashboard identifier');
    }

    // Normalize and sanitize inputs
    $breakdown = [];
    if (isset($payload['breakdown']) && is_array($payload['breakdown'])) {
        foreach ($payload['breakdown'] as $type => $count) {
            $t = trim((string)$type);
            if ($t === '') { continue; }
            $cnt = (int)$count;
            if ($cnt < 0) { $cnt = 0; }
            $breakdown[$t] = $cnt;
        }
    }

    $contacts = [];
    if (isset($payload['contacts']) && is_array($payload['contacts'])) {
        // contacts is expected to be an object keyed by type => array of contacts
        foreach ($payload['contacts'] as $type => $list) {
            if (!is_array($list)) { continue; }
            $t = trim((string)$type);
            $contacts[$t] = [];
            foreach ($list as $c) {
                if (!is_array($c)) { continue; }
                $name = trim((string)($c['name'] ?? ''));
                $phoneCode = trim((string)($c['phone_code'] ?? ''));
                $phone = trim((string)($c['phone'] ?? ''));
                $kingschat = trim((string)($c['kingschat'] ?? ''));
                $email = trim((string)($c['email'] ?? ''));
                if ($name === '' && $phone === '' && $email === '' && $kingschat === '') { continue; }
                $contacts[$t][] = [
                    'name' => $name,
                    'phone_code' => $phoneCode,
                    'phone' => $phone,
                    'kingschat' => $kingschat,
                    'email' => $email,
                ];
            }
        }
    }

    $venues = [];
    if (isset($payload['venues']) && is_array($payload['venues'])) {
        // venues is expected to be an object keyed by type => array of venues
        foreach ($payload['venues'] as $type => $list) {
            if (!is_array($list)) { continue; }
            $t = trim((string)$type);
            $venues[$t] = [];
            foreach ($list as $v) {
                if (!is_array($v)) { continue; }
                $venueName = trim((string)($v['venue'] ?? ''));
                $location = trim((string)($v['location'] ?? ''));
                $date = trim((string)($v['date'] ?? ''));

                // Optional nested contacts per venue (accountability)
                $venueContacts = [];
                if (isset($v['contacts']) && is_array($v['contacts'])) {
                    foreach ($v['contacts'] as $c) {
                        if (!is_array($c)) { continue; }
                        $name = trim((string)($c['name'] ?? ''));
                        $phoneCode = trim((string)($c['phone_code'] ?? ''));
                        $phone = trim((string)($c['phone'] ?? ''));
                        $kingschat = trim((string)($c['kingschat'] ?? ''));
                        $email = trim((string)($c['email'] ?? ''));
                        if ($name === '' && $phone === '' && $email === '' && $kingschat === '') { continue; }
                        $venueContacts[] = [
                            'name' => $name,
                            'phone_code' => $phoneCode,
                            'phone' => $phone,
                            'kingschat' => $kingschat,
                            'email' => $email,
                        ];
                    }
                }

                if ($venueName === '' && $location === '' && $date === '' && empty($venueContacts)) { continue; }
                $venueItem = [
                    'venue' => $venueName,
                    'location' => $location,
                    'date' => $date,
                ];
                if (!empty($venueContacts)) {
                    $venueItem['contacts'] = $venueContacts;
                }
                $venues[$t][] = $venueItem;
            }
        }
    }

    // Prepare separate file storage per dashboard/key
    $safe = function (string $s): string {
        $s = trim($s);
        $s = preg_replace('/[^A-Za-z0-9_\-]+/', '_', $s);
        return trim($s, '_');
    };
    $separateDir = __DIR__ . '/zone_breakdowns';
    if (!is_dir($separateDir)) { @mkdir($separateDir, 0755, true); }
    $separateFile = $separateDir . '/' . $safe($dashboardType) . '__' . $safe($key) . '.json';

    $separateData = [
        'dashboard_type' => $dashboardType,
        'key' => $key,
        'breakdown' => $breakdown,
        'contacts' => $contacts,
        'venues' => $venues,
        'updated_at' => date('c'),
    ];
    $jsonSeparate = json_encode($separateData, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE);
    if ($jsonSeparate === false) {
        throw new Exception('Failed to encode data');
    }
    // Write separate file with lock
    $sepTmp = $separateFile . '.tmp';
    $sf = @fopen($sepTmp, 'c');
    if ($sf && flock($sf, LOCK_EX)) {
        ftruncate($sf, 0);
        fwrite($sf, $jsonSeparate);
        fflush($sf);
        flock($sf, LOCK_UN);
        fclose($sf);
        @rename($sepTmp, $separateFile);
    } else {
        if ($sf) { fclose($sf); }
        // Fallback direct write
        if (file_put_contents($separateFile, $jsonSeparate, LOCK_EX) === false) {
            throw new Exception('Failed to write separate file');
        }
    }

    // Also maintain aggregate file for backward compatibility
    $storeFile = __DIR__ . '/zone_breakdowns.json';
    $store = [];
    if (file_exists($storeFile)) {
        $existing = file_get_contents($storeFile);
        $decoded = json_decode($existing, true);
        if (is_array($decoded)) {
            $store = $decoded;
        }
    }
    $storeKey = $dashboardType . '|' . $key;
    $store[$storeKey] = $separateData;
    $jsonOut = json_encode($store, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE);
    if ($jsonOut !== false) {
        if (file_exists($storeFile)) { @copy($storeFile, $storeFile . '.bak_' . date('Ymd_His')); }
        $tmpFile = $storeFile . '.tmp';
        $fp = @fopen($tmpFile, 'c');
        if ($fp && flock($fp, LOCK_EX)) {
            ftruncate($fp, 0);
            fwrite($fp, $jsonOut);
            fflush($fp);
            flock($fp, LOCK_UN);
            fclose($fp);
            @rename($tmpFile, $storeFile);
        } else {
            if ($fp) { fclose($fp); }
            @file_put_contents($storeFile, $jsonOut, LOCK_EX);
        }
    }

    echo json_encode(['success' => true, 'updated_at' => date('c'), 'file' => basename($separateFile)]);
} catch (Throwable $e) {
    http_response_code(400);
    echo json_encode(['success' => false, 'error' => $e->getMessage()]);
}


