RewriteEngine On

# Remove .php extension from URLs
RewriteCond %{REQUEST_FILENAME} !-d
RewriteCond %{REQUEST_FILENAME} !-f
RewriteRule ^([^\.]+)$ $1.php [NC,L]

# Redirect .php extension to clean URLs (but exclude handler files to preserve POST method)
RewriteCond %{THE_REQUEST} /([^.]+)\.php [NC]
RewriteCond %{REQUEST_URI} !-handler\.php$ [NC]
RewriteCond %{REQUEST_URI} !generate-update-link\.php$ [NC]
RewriteCond %{REQUEST_URI} !save-zone-breakdown\.php$ [NC]
RewriteRule ^ /%1 [NC,L,R=301]

# Specific rule for admin-backend-links
RewriteRule ^admin-backend-links$ admin-backend-links.php [NC,L]
RewriteRule ^admin-backend-links/$ admin-backend-links.php [NC,L]

# Handler files should keep their .php extension to preserve POST method
RewriteRule ^register-church-handler$ register-church-handler.php [NC,L]
RewriteRule ^register-network-handler$ register-network-handler.php [NC,L]

# Other common pages
RewriteRule ^admin-dashboard$ admin-dashboard.php [NC,L]
RewriteRule ^public-dashboard$ public-dashboard.php [NC,L]
RewriteRule ^zone-dashboard$ zone-dashboard.php [NC,L]
RewriteRule ^register-church$ register-church.php [NC,L]
RewriteRule ^register-network$ register-network.php [NC,L]
RewriteRule ^update-dashboard$ update-dashboard.php [NC,L]
RewriteRule ^generate-update-link$ generate-update-link.php [NC,L]

# Block direct access to sensitive directories
RewriteRule ^includes/ - [F]

# Allow data.json for dashboards but block backups
RewriteRule ^data\.json\.bak - [F]