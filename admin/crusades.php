<?php
// IP Guard switcher UI and persistence
// Allow admin to toggle guard via simple switch stored in data/settings.json
if (!function_exists('getSettings')) {
    function getSettings(): array {
        $path = __DIR__ . '/../data/settings.json';
        if (is_readable($path)) {
            $json = file_get_contents($path);
            $data = json_decode($json, true);
            if (json_last_error() === JSON_ERROR_NONE && is_array($data)) {
                return $data;
            }
        }
        return [];
    }
}

if (!function_exists('saveSettings')) {
    function saveSettings(array $settings): bool {
        $path = __DIR__ . '/../data/settings.json';
        $dir = dirname($path);
        if (!is_dir($dir)) {
            @mkdir($dir, 0755, true);
        }
        $json = json_encode($settings, JSON_PRETTY_PRINT | JSON_UNESCAPED_SLASHES);
        return file_put_contents($path, $json) !== false;
    }
}
// Enable error reporting for debugging
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Start session for admin authentication
session_start();

// Check if admin is logged in
if (!isset($_SESSION['admin_logged_in'])) {
    header('Location: login.php');
    exit();
}

// Handle CSV export for all tabs before any HTML output
if (isset($_GET['export']) && $_GET['export'] === 'csv') {
    $tab = $_GET['tab'] ?? '';
    
    if ($tab === 'world-requests') {
        // Handle World Requests CSV export
        function getWorldCrusadeRequests($file) {
            if (!file_exists($file)) {
                return ['requests' => []];
            }
            $json = file_get_contents($file);
            $data = json_decode($json, true);
            return $data ?: ['requests' => []];
        }
        
        // Define file path
        $worldRequestsFile = __DIR__ . '/../data/world_crusade_requests.json';
        
        // Get world crusade requests
        $worldRequestsData = getWorldCrusadeRequests($worldRequestsFile);
        $worldRequests = $worldRequestsData['requests'] ?? [];
        
        // For export, ignore filters and export all requests
        $filteredRequests = $worldRequests;
        
        // Sort by creation date (newest first)
        usort($filteredRequests, function($a, $b) {
            return strtotime($b['created_at'] ?? '1970-01-01') - strtotime($a['created_at'] ?? '1970-01-01');
        });
        
        // Set headers for CSV download
        header('Content-Type: text/csv');
        header('Content-Disposition: attachment; filename="world_crusade_requests_' . date('Y-m-d') . '.csv"');
        
        $output = fopen('php://output', 'w');
        
        // CSV headers
        fputcsv($output, [
            'ID', 'Created Date', 'Registration Type', 'Requester Name', 'Organization Name', 'Designation', 'Email', 'Phone', 
            'Zone', 'Group', 'Church', 'Church Type', 'Ministry Name', 'Crusade Title', 'Country', 'Location', 'Crusade Type', 
            'Venue', 'Expected Attendance', 'Number of Crusades', 'Comments', 'Additional Comments', 'Status', 'Admin Notes'
        ]);
        
        // CSV data
        foreach ($filteredRequests as $request) {
            $registrationType = $request['registration_type'] ?? 'individual';
            
            // Determine requester name based on registration type (matching the table display logic)
            $requesterName = '';
            if ($registrationType === 'organisation' || $registrationType === 'network') {
                $requesterName = $request['organization_name'] ?? 'Organisation';
            } elseif ($registrationType === 'church') {
                if (!empty($request['first_name']) && !empty($request['last_name'])) {
                    $designation = !empty($request['designation']) ? ucfirst($request['designation']) . ' ' : '';
                    $requesterName = $designation . $request['first_name'] . ' ' . $request['last_name'];
                } else {
                    $churchName = $request['church_name'] ?? $request['ministry_name'] ?? $request['group_name'] ?? 'Church Contact';
                    $requesterName = $churchName;
                }
            } else {
                $designation = !empty($request['designation']) ? ucfirst($request['designation']) . ' ' : '';
                $firstName = $request['first_name'] ?? '';
                $lastName = $request['last_name'] ?? '';
                $requesterName = $designation . $firstName . ' ' . $lastName;
            }
            
            fputcsv($output, [
                $request['id'] ?? '',
                $request['created_at'] ?? '',
                $registrationType,
                trim($requesterName),
                $request['organization_name'] ?? '',
                $request['designation'] ?? '',
                $request['email'] ?? '',
                $request['phone'] ?? '',
                $request['zone'] ?? '',
                $request['group'] ?? $request['church_group_name'] ?? '',
                $request['church'] ?? $request['church_name'] ?? '',
                $request['church_type'] ?? '',
                $request['ministry_name'] ?? '',
                $request['crusade_title'] ?? '',
                $request['country'] ?? '',
                $request['location'] ?? '',
                $request['crusade_type'] ?? '',
                $request['venue'] ?? '',
                $request['attendance'] ?? '',
                $request['number_of_crusades'] ?? '',
                $request['comments'] ?? '',
                $request['additional_comments'] ?? '',
                $request['status'] ?? 'pending',
                $request['admin_notes'] ?? ''
            ]);
        }
        
        fclose($output);
        exit();
    }
}

// Define the path to the JSON file
$jsonFile = __DIR__ . '/../data/crusades.json';

// Function to read crusades from JSON file
function getCrusades($file) {
    if (!file_exists($file)) {
        return ['crusades' => []];
    }
    $json = file_get_contents($file);
    return json_decode($json, true);
}

// Function to save crusades to JSON file
function saveCrusades($file, $data) {
    $json = json_encode($data, JSON_PRETTY_PRINT | JSON_UNESCAPED_SLASHES);
    return file_put_contents($file, $json) !== false;
}

// Handle form submissions
$message = '';
$messageType = '';

// Debug: Log POST data
error_log('POST data: ' . print_r($_POST, true));
error_log('FILES data: ' . print_r($_FILES, true));
error_log('REQUEST_METHOD: ' . $_SERVER['REQUEST_METHOD']);

// Check if form was submitted
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    // Handle IP Guard toggle from header switch
    if (isset($_POST['save_ip_guard'])) {
        $settings = getSettings();
        $newValue = isset($_POST['ip_guard_enabled']) && $_POST['ip_guard_enabled'] === '1';
        $settings['ip_guard_enabled'] = $newValue;
        if (saveSettings($settings)) {
            $_SESSION['message'] = 'IP Guard ' . ($newValue ? 'enabled' : 'disabled') . ' successfully!';
            $_SESSION['messageType'] = 'success';
        } else {
            $_SESSION['message'] = 'Failed to save IP Guard setting.';
            $_SESSION['messageType'] = 'error';
        }
        header('Location: ' . $_SERVER['PHP_SELF'] . (isset($_GET['tab']) ? '?tab=' . urlencode($_GET['tab']) : ''));
        exit();
    }
    error_log('Form submitted with action: ' . ($_POST['action'] ?? 'no action'));
    
    // Verify JSON file is writable
    if (!is_writable($jsonFile)) {
        error_log('JSON file is not writable: ' . $jsonFile);
        $message = 'Error: The data file is not writable. Please check file permissions.';
        $messageType = 'error';
    } else {
        error_log('JSON file is writable');
    }
    $crusades = getCrusades($jsonFile);
    
    // Add New Crusade
    if (isset($_POST['action']) && $_POST['action'] === 'add_crusade') {
        // Generate a new ID
        $newId = 1;
        if (!empty($crusades['crusades'])) {
            $ids = array_column($crusades['crusades'], 'id');
            $newId = max($ids) + 1;
        }
        
        $newCrusade = handleCrusadeSubmission($newId);
        
        if ($newCrusade) {
            array_unshift($crusades['crusades'], $newCrusade); // Add to beginning of array
            
            if (saveCrusades($jsonFile, $crusades)) {
                // Store success message in session and redirect to prevent form resubmission
                $_SESSION['message'] = 'Crusade added successfully!';
                $_SESSION['messageType'] = 'success';
                header('Location: ' . $_SERVER['PHP_SELF']);
                exit();
            } else {
                $message = 'Error saving crusade. Please try again.';
                $messageType = 'error';
            }
        }
    }
    // Update Existing Crusade
    elseif (isset($_POST['action']) && $_POST['action'] === 'update_crusade' && isset($_POST['crusade_id'])) {
        $crusadeId = (int)$_POST['crusade_id'];
        $crusadeIndex = array_search($crusadeId, array_column($crusades['crusades'], 'id'));
        
        if ($crusadeIndex !== false) {
            $updatedCrusade = handleCrusadeSubmission($crusadeId);
            
            if ($updatedCrusade) {
                $crusades['crusades'][$crusadeIndex] = $updatedCrusade;
                
                if (saveCrusades($jsonFile, $crusades)) {
                    // Store success message in session and redirect to prevent form resubmission
                    $_SESSION['message'] = 'Crusade updated successfully!';
                    $_SESSION['messageType'] = 'success';
                    header('Location: ' . $_SERVER['PHP_SELF']);
                    exit();
                } else {
                    $message = 'Error updating crusade. Please try again.';
                    $messageType = 'error';
                }
            }
        }
    }
}

// Handle Delete Request
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action']) && $_POST['action'] === 'delete' && isset($_POST['crusade_id'])) {
    error_log('Delete request received for ID: ' . $_POST['crusade_id']);
    
    $crusades = getCrusades($jsonFile);
    $crusadeId = (int)$_POST['crusade_id'];
    
    error_log('Looking for crusade ID: ' . $crusadeId);
    error_log('Current crusades: ' . print_r(array_column($crusades['crusades'] ?? [], 'id'), true));
    
    $crusadeIndex = null;
    foreach ($crusades['crusades'] as $index => $crusade) {
        if ((int)$crusade['id'] === $crusadeId) {
            $crusadeIndex = $index;
            break;
        }
    }
    
    if ($crusadeIndex !== null) {
        error_log('Found crusade at index: ' . $crusadeIndex);
        // Remove the crusade
        array_splice($crusades['crusades'], $crusadeIndex, 1);
        
        if (saveCrusades($jsonFile, $crusades)) {
            $_SESSION['message'] = 'Crusade deleted successfully!';
            $_SESSION['messageType'] = 'success';
            error_log('Crusade deleted successfully');
        } else {
            $_SESSION['message'] = 'Error deleting crusade. Please check file permissions.';
            $_SESSION['messageType'] = 'error';
            error_log('Error saving after delete');
        }
    } else {
        $_SESSION['message'] = 'Crusade not found.';
        $_SESSION['messageType'] = 'error';
        error_log('Crusade not found');
    }
    
    // Redirect to prevent form resubmission
    header('Location: ' . $_SERVER['PHP_SELF']);
    exit();
}

// Handle messages from session or URL
if (isset($_SESSION['message'])) {
    $message = $_SESSION['message'];
    $messageType = $_SESSION['messageType'];
    // Clear the session messages
    unset($_SESSION['message']);
    unset($_SESSION['messageType']);
} elseif (isset($_GET['message']) && isset($_GET['type'])) {
    $message = $_GET['message'];
    $messageType = $_GET['type'];
}

// Function to generate a slug from title
function generateSlug($title) {
    // Convert to lowercase
    $slug = strtolower($title);
    // Replace spaces and special characters with hyphens
    $slug = preg_replace('/[^a-z0-9]+/', '-', $slug);
    // Remove leading/trailing hyphens
    $slug = trim($slug, '-');
    // Limit length to 50 characters
    $slug = substr($slug, 0, 50);
    // Remove trailing hyphen if created by substr
    $slug = rtrim($slug, '-');

    return $slug;
}

// Function to handle crusade form submission (add/update)
function handleCrusadeSubmission($id) {
    $imagePath = 'assets/images/gloria.jpg'; // Default image

    // Handle file upload if a new image is provided
    if (isset($_FILES['image']) && $_FILES['image']['error'] === UPLOAD_ERR_OK) {
        $uploadDir = __DIR__ . '/../assets/images/';

        // Ensure upload directory exists and is writable
        if (!is_dir($uploadDir)) {
            if (!mkdir($uploadDir, 0755, true)) {
                error_log("Failed to create directory: $uploadDir");
            }
        }

        // Validate file type
        $allowedTypes = ['image/jpeg', 'image/png', 'image/gif'];
        $fileType = mime_content_type($_FILES['image']['tmp_name']);

        if (!in_array($fileType, $allowedTypes)) {
            error_log("Invalid file type: $fileType");
        } else {
            // Generate a safe filename
            $fileName = uniqid() . '_' . preg_replace('/[^a-zA-Z0-9_\-\.]/', '', $_FILES['image']['name']);
            $targetPath = $uploadDir . $fileName;

            if (move_uploaded_file($_FILES['image']['tmp_name'], $targetPath)) {
                $imagePath = 'assets/images/' . $fileName;
                error_log("Successfully uploaded image to: $imagePath");
            } else {
                error_log("Failed to move uploaded file. Error: " . print_r(error_get_last(), true));
            }
        }
    } elseif (!empty($_POST['current_image'])) {
        // Keep the existing image if no new one is uploaded
        $imagePath = $_POST['current_image'];
        error_log("Using existing image: $imagePath");
    }

    // Handle register link generation
    $registerLink = '';
    if (!empty($_POST['register_link'])) {
        // Use custom register link provided by user
        $registerLink = $_POST['register_link'];
    } else {
        // Generate register link from title slug
        $slug = generateSlug($_POST['title']);
        if (!empty($slug)) {
            $registerLink = 'register.php?event=' . urlencode($slug);
        }
    }

    // Create/update crusade data
    return [
        'id' => $id,
        'title' => $_POST['title'],
        'date' => $_POST['date'],
        'time' => $_POST['time'],
        'venue' => $_POST['venue'],
        'address' => $_POST['address'],
        'image' => $imagePath ?: 'assets/images/gloria.jpg',
        'description' => $_POST['description'],
        'register_link' => $registerLink
    ];
}

// Get current crusades for display
$crusades = getCrusades($jsonFile);

// Handle CSV export for registrations
if (isset($_GET['export']) && $_GET['export'] === 'csv' && isset($_GET['tab']) && $_GET['tab'] === 'registrations') {
    $registrationsFile = __DIR__ . '/../data/registrations.json';
    if (file_exists($registrationsFile)) {
        $registrations = json_decode(file_get_contents($registrationsFile), true);

        header('Content-Type: text/csv');
        header('Content-Disposition: attachment; filename="registrations_' . date('Y-m-d') . '.csv"');

        $output = fopen('php://output', 'w');

        // CSV headers
        fputcsv($output, [
            'ID', 'Registration Date', 'Title', 'First Name', 'Last Name', 'Email', 'Phone', 'Address',
            'Crusade', 'Crusade Date', 'Venue', 'Attending', 'Coming With', 'Inviting',
            'Source', 'Church', 'Healing', 'Prayer', 'Prayer Details', 'Rhapsody Interested', 'Copies'
        ]);

        // CSV data
        foreach ($registrations['registrations'] as $reg) {
            fputcsv($output, [
                $reg['id'], $reg['timestamp'], $reg['title'], $reg['first_name'], $reg['last_name'],
                $reg['email'], $reg['phone'], $reg['address'], $reg['crusade_title'], $reg['crusade_date'],
                $reg['crusade_venue'], $reg['attending'], $reg['coming_with'], $reg['inviting'],
                $reg['source'], $reg['church'], $reg['healing'], $reg['prayer'], $reg['prayer_details'],
                $reg['rhapsody_interested'], $reg['copies']
            ]);
        }

        fclose($output);
        exit();
    }
}

// Handle category form submissions
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action']) && in_array($_POST['action'], ['add_category', 'delete_category'])) {
    $categoriesJsonFile = __DIR__ . '/../data/categories.json';
    
    // Function to read categories from JSON file
    function getCategories($file) {
        if (!file_exists($file)) {
            return ['categories' => []];
        }
        $json = file_get_contents($file);
        return json_decode($json, true);
    }
    
    // Function to save categories to JSON file
    function saveCategories($file, $data) {
        $json = json_encode($data, JSON_PRETTY_PRINT | JSON_UNESCAPED_SLASHES);
        return file_put_contents($file, $json) !== false;
    }
    
    $categories = getCategories($categoriesJsonFile);
    
    // Add New Category
    if ($_POST['action'] === 'add_category') {
        // Generate a new ID
        $newId = 1;
        if (!empty($categories['categories'])) {
            $ids = array_column($categories['categories'], 'id');
            $newId = max($ids) + 1;
        }
        
        // Generate name from display_name
        $name = strtolower(trim($_POST['display_name']));
        $name = preg_replace('/[^a-z0-9]+/', '_', $name);
        $name = trim($name, '_');
        
        // Check if category name already exists
        $nameExists = false;
        foreach ($categories['categories'] as $category) {
            if ($category['name'] === $name) {
                $nameExists = true;
                break;
            }
        }
        
        if ($nameExists) {
            $_SESSION['message'] = 'Category with this name already exists.';
            $_SESSION['messageType'] = 'error';
        } else {
            $newCategory = [
                'id' => $newId,
                'name' => $name,
                'display_name' => $_POST['display_name'],
                'description' => $_POST['description'],
                'created_at' => date('Y-m-d')
            ];
            
            array_unshift($categories['categories'], $newCategory);
            
            if (saveCategories($categoriesJsonFile, $categories)) {
                $_SESSION['message'] = 'Category added successfully!';
                $_SESSION['messageType'] = 'success';
            } else {
                $_SESSION['message'] = 'Error saving category. Please check file permissions.';
                $_SESSION['messageType'] = 'error';
            }
        }
        
        header('Location: ' . $_SERVER['PHP_SELF'] . '?tab=media');
        exit();
    }
    
    // Delete Category
    elseif ($_POST['action'] === 'delete_category' && isset($_POST['category_id'])) {
        $categoryId = (int)$_POST['category_id'];
        $categoryIndex = null;
        
        foreach ($categories['categories'] as $index => $category) {
            if ((int)$category['id'] === $categoryId) {
                $categoryIndex = $index;
                break;
            }
        }
        
        if ($categoryIndex !== null) {
            array_splice($categories['categories'], $categoryIndex, 1);
            
            if (saveCategories($categoriesJsonFile, $categories)) {
                $_SESSION['message'] = 'Category deleted successfully!';
                $_SESSION['messageType'] = 'success';
            } else {
                $_SESSION['message'] = 'Error deleting category. Please check file permissions.';
                $_SESSION['messageType'] = 'error';
            }
        } else {
            $_SESSION['message'] = 'Category not found.';
            $_SESSION['messageType'] = 'error';
        }
        
        header('Location: ' . $_SERVER['PHP_SELF'] . '?tab=media');
        exit();
    }
}

// Handle media form submissions
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action']) && in_array($_POST['action'], ['add_media', 'update_media', 'delete_media'])) {
    $mediaJsonFile = __DIR__ . '/../data/media.json';
    
    // Function to read media from JSON file
    function getMedia($file) {
        if (!file_exists($file)) {
            return ['media' => []];
        }
        $json = file_get_contents($file);
        return json_decode($json, true);
    }
    
    // Function to save media to JSON file
    function saveMedia($file, $data) {
        $json = json_encode($data, JSON_PRETTY_PRINT | JSON_UNESCAPED_SLASHES);
        return file_put_contents($file, $json) !== false;
    }
    
    $media = getMedia($mediaJsonFile);
    
    // Add New Media
    if ($_POST['action'] === 'add_media') {
        // Generate a new ID
        $newId = 1;
        if (!empty($media['media'])) {
            $ids = array_column($media['media'], 'id');
            $newId = max($ids) + 1;
        }
        
        $newMedia = [
            'id' => $newId,
            'title' => $_POST['title'],
            'description' => $_POST['description'],
            'type' => $_POST['type'] ?? 'video', // video or document
            'video_url' => $_POST['video_url'] ?? '',
            'document_url' => $_POST['document_url'] ?? '',
            'thumbnail' => $_POST['thumbnail'] ?? '',
            'date_added' => $_POST['date_added'],
            'category' => $_POST['category'],
            'featured' => isset($_POST['featured']) ? true : false
        ];
        
        array_unshift($media['media'], $newMedia);
        
        if (saveMedia($mediaJsonFile, $media)) {
            $_SESSION['message'] = 'Media added successfully!';
            $_SESSION['messageType'] = 'success';
        } else {
            $_SESSION['message'] = 'Error saving media. Please check file permissions.';
            $_SESSION['messageType'] = 'error';
        }
        
        header('Location: ' . $_SERVER['PHP_SELF'] . '?tab=media');
        exit();
    }
    
    // Update Media
    elseif ($_POST['action'] === 'update_media' && isset($_POST['media_id'])) {
        $mediaId = (int)$_POST['media_id'];
        $mediaIndex = null;
        
        foreach ($media['media'] as $index => $mediaItem) {
            if ((int)$mediaItem['id'] === $mediaId) {
                $mediaIndex = $index;
                break;
            }
        }
        
        if ($mediaIndex !== null) {
            $updatedMedia = [
                'id' => $mediaId,
                'title' => $_POST['title'],
                'description' => $_POST['description'],
                'type' => $_POST['type'] ?? 'video', // video or document
                'video_url' => $_POST['video_url'] ?? '',
                'document_url' => $_POST['document_url'] ?? '',
                'thumbnail' => $_POST['thumbnail'] ?? '',
                'date_added' => $_POST['date_added'],
                'category' => $_POST['category'],
                'featured' => isset($_POST['featured']) ? true : false
            ];
            
            $media['media'][$mediaIndex] = $updatedMedia;
            
            if (saveMedia($mediaJsonFile, $media)) {
                $_SESSION['message'] = 'Media updated successfully!';
                $_SESSION['messageType'] = 'success';
            } else {
                $_SESSION['message'] = 'Error updating media. Please check file permissions.';
                $_SESSION['messageType'] = 'error';
            }
        } else {
            $_SESSION['message'] = 'Media not found.';
            $_SESSION['messageType'] = 'error';
        }
        
        header('Location: ' . $_SERVER['PHP_SELF'] . '?tab=media');
        exit();
    }
    
    // Delete Media
    elseif ($_POST['action'] === 'delete_media' && isset($_POST['media_id'])) {
        $mediaId = (int)$_POST['media_id'];
        $mediaIndex = null;
        
        foreach ($media['media'] as $index => $mediaItem) {
            if ((int)$mediaItem['id'] === $mediaId) {
                $mediaIndex = $index;
                break;
            }
        }
        
        if ($mediaIndex !== null) {
            array_splice($media['media'], $mediaIndex, 1);
            
            if (saveMedia($mediaJsonFile, $media)) {
                $_SESSION['message'] = 'Media deleted successfully!';
                $_SESSION['messageType'] = 'success';
            } else {
                $_SESSION['message'] = 'Error deleting media. Please check file permissions.';
                $_SESSION['messageType'] = 'error';
            }
        } else {
            $_SESSION['message'] = 'Media not found.';
            $_SESSION['messageType'] = 'error';
        }
        
        header('Location: ' . $_SERVER['PHP_SELF'] . '?tab=media');
        exit();
    }
}

// Handle highlights form submissions
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action']) && in_array($_POST['action'], ['add_highlight', 'update_highlight', 'delete_highlight'])) {
    // Include highlights functions
    $highlightsJsonFile = __DIR__ . '/../data/highlights.json';
    
    // Function to read highlights from JSON file
    function getHighlights($file) {
        if (!file_exists($file)) {
            return ['highlights' => []];
        }
        $json = file_get_contents($file);
        return json_decode($json, true);
    }
    
    // Function to save highlights to JSON file
    function saveHighlights($file, $data) {
        $json = json_encode($data, JSON_PRETTY_PRINT | JSON_UNESCAPED_SLASHES);
        return file_put_contents($file, $json) !== false;
    }
    
    $highlights = getHighlights($highlightsJsonFile);
    
    // Add New Highlight
    if ($_POST['action'] === 'add_highlight') {
        // Generate a new ID
        $newId = 1;
        if (!empty($highlights['highlights'])) {
            $ids = array_column($highlights['highlights'], 'id');
            $newId = max($ids) + 1;
        }
        
        // Process multiple images
        $images = [];
        if (!empty($_POST['images'])) {
            $imageLines = explode("\n", $_POST['images']);
            foreach ($imageLines as $line) {
                $line = trim($line);
                if (!empty($line)) {
                    $images[] = $line;
                }
            }
        }
        
        // Process multiple videos
        $videos = [];
        if (!empty($_POST['videos'])) {
            $videoLines = explode("\n", $_POST['videos']);
            foreach ($videoLines as $line) {
                $line = trim($line);
                if (!empty($line)) {
                    $videos[] = $line;
                }
            }
        }
        
        $newHighlight = [
            'id' => $newId,
            'title' => $_POST['title'],
            'description' => $_POST['description'],
            'category' => $_POST['category'],
            'badge' => $_POST['badge'],
            'badge_color' => $_POST['badge_color'],
            'date' => $_POST['date'],
            'images' => $images,
            'videos' => $videos,
            'location' => $_POST['location'],
            'auto_translate' => true
        ];
        
        array_unshift($highlights['highlights'], $newHighlight);
        
        if (saveHighlights($highlightsJsonFile, $highlights)) {
            $_SESSION['message'] = 'Highlight added successfully!';
            $_SESSION['messageType'] = 'success';
        } else {
            $_SESSION['message'] = 'Error saving highlight. Please check file permissions.';
            $_SESSION['messageType'] = 'error';
        }
        
        header('Location: ' . $_SERVER['PHP_SELF'] . '?tab=highlights');
        exit();
    }
    
    // Update Highlight
    elseif ($_POST['action'] === 'update_highlight' && isset($_POST['highlight_id'])) {
        // Validate required fields
        $requiredFields = ['title', 'description', 'category', 'badge', 'badge_color', 'date', 'location', 'images'];
        $missingFields = [];
        
        foreach ($requiredFields as $field) {
            if (empty($_POST[$field])) {
                $missingFields[] = $field;
            }
        }
        
        if (!empty($missingFields)) {
            $_SESSION['message'] = 'Missing required fields: ' . implode(', ', $missingFields);
            $_SESSION['messageType'] = 'error';
            header('Location: ' . $_SERVER['PHP_SELF'] . '?tab=highlights');
            exit();
        }
        
        $highlightId = (int)$_POST['highlight_id'];
        $highlightIndex = null;
        
        foreach ($highlights['highlights'] as $index => $highlight) {
            if ((int)$highlight['id'] === $highlightId) {
                $highlightIndex = $index;
                break;
            }
        }
        
        if ($highlightIndex !== null) {
            // Process multiple images
            $images = [];
            if (!empty($_POST['images'])) {
                $imageLines = explode("\n", $_POST['images']);
                foreach ($imageLines as $line) {
                    $line = trim($line);
                    if (!empty($line)) {
                        $images[] = $line;
                    }
                }
            }
            
            // Process multiple videos
            $videos = [];
            if (!empty($_POST['videos'])) {
                $videoLines = explode("\n", $_POST['videos']);
                foreach ($videoLines as $line) {
                    $line = trim($line);
                    if (!empty($line)) {
                        $videos[] = $line;
                    }
                }
            }
            
            $updatedHighlight = [
                'id' => $highlightId,
                'title' => $_POST['title'],
                'description' => $_POST['description'],
                'category' => $_POST['category'],
                'badge' => $_POST['badge'],
                'badge_color' => $_POST['badge_color'],
                'date' => $_POST['date'],
                'images' => $images,
                'videos' => $videos,
                'location' => $_POST['location'],
                'auto_translate' => true
            ];
            
            $highlights['highlights'][$highlightIndex] = $updatedHighlight;
            
            if (saveHighlights($highlightsJsonFile, $highlights)) {
                $_SESSION['message'] = 'Highlight updated successfully!';
                $_SESSION['messageType'] = 'success';
            } else {
                $_SESSION['message'] = 'Error updating highlight. Please check file permissions.';
                $_SESSION['messageType'] = 'error';
            }
        } else {
            $_SESSION['message'] = 'Highlight not found.';
            $_SESSION['messageType'] = 'error';
        }
        
        header('Location: ' . $_SERVER['PHP_SELF'] . '?tab=highlights');
        exit();
    }
    
    // Delete Highlight
    elseif ($_POST['action'] === 'delete_highlight' && isset($_POST['highlight_id'])) {
        $highlightId = (int)$_POST['highlight_id'];
        $highlightIndex = null;
        
        foreach ($highlights['highlights'] as $index => $highlight) {
            if ((int)$highlight['id'] === $highlightId) {
                $highlightIndex = $index;
                break;
            }
        }
        
        if ($highlightIndex !== null) {
            array_splice($highlights['highlights'], $highlightIndex, 1);
            
            if (saveHighlights($highlightsJsonFile, $highlights)) {
                $_SESSION['message'] = 'Highlight deleted successfully!';
                $_SESSION['messageType'] = 'success';
            } else {
                $_SESSION['message'] = 'Error deleting highlight. Please check file permissions.';
                $_SESSION['messageType'] = 'error';
            }
        } else {
            $_SESSION['message'] = 'Highlight not found.';
            $_SESSION['messageType'] = 'error';
        }
        
        header('Location: ' . $_SERVER['PHP_SELF'] . '?tab=highlights');
        exit();
    }
}

// Get current tab
$currentTab = $_GET['tab'] ?? 'crusades';
?>

<!-- Add tab styles -->
<style>
.tab-button {
    padding: 0.5rem 1rem;
    font-size: 0.875rem;
    font-weight: 500;
    border: none;
    border-radius: 0.5rem 0.5rem 0 0;
    border-bottom: 2px solid transparent;
    color: #6b7280;
    background: transparent;
    cursor: pointer;
    transition: all 0.2s;
    outline: none;
}
.tab-button:hover {
    color: #374151;
    border-bottom-color: #d1d5db;
}
.tab-button.active {
    color: #dc2626;
    border-bottom-color: #dc2626;
    background-color: white;
}
.tab-content {
    display: none;
}
.tab-content.active {
    display: block;
}
</style>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Manage Crusades - Admin</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: '#dc2626', // Red-600
                        'primary-dark': '#b91c1c', // Red-700
                    },
                },
            },
        }
    </script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <script>
    function validateForm() {
        console.log('Form validation called');
        const requiredFields = ['title', 'date', 'time', 'venue', 'address', 'description'];
        let isValid = true;
        
        requiredFields.forEach(field => {
            const element = document.getElementById(field);
            if (element && !element.value.trim()) {
                alert(`Please fill in the ${field} field`);
                element.focus();
                isValid = false;
                return false;
            }
        });
        
        if (!isValid) {
            return false;
        }
        
        // Show loading state
        const submitBtn = document.querySelector('button[type="submit"]');
        if (submitBtn) {
            submitBtn.disabled = true;
            submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Processing...';
        }
        
        return true;
    }
    
    function confirmDelete(event) {
        if (!confirm('Are you sure you want to delete this crusade? This action cannot be undone.')) {
            event.preventDefault();
            return false;
        }
        return true;
    }

    // Tab functionality
    function switchTab(tabName) {
        // Update URL with the selected tab
        const url = new URL(window.location);
        url.searchParams.set('tab', tabName);
        window.history.pushState({}, '', url);
        
        // Hide all tab contents
        document.querySelectorAll('.tab-content').forEach(content => {
            content.classList.remove('active');
        });

        // Remove active class from all tab buttons
        document.querySelectorAll('.tab-button').forEach(button => {
            button.classList.remove('active');
        });

        // Show selected tab content
        const selectedTab = document.getElementById(tabName + '-tab');
        if (selectedTab) {
            selectedTab.classList.add('active');
        }

        // Add active class to clicked button
        const selectedButton = document.querySelector(`[onclick*="switchTab('${tabName}')"]`);
        if (selectedButton) {
            selectedButton.classList.add('active');
        }
    }

    // Initialize tabs on page load
    document.addEventListener('DOMContentLoaded', function() {
        const urlParams = new URLSearchParams(window.location.search);
        const activeTab = urlParams.get('tab') || 'crusades';
        switchTab(activeTab);
    });

    // Handle filter form submission for registrations
    function applyFilters(event) {
        // Let the form submit normally, but ensure we stay on registrations tab
        const form = event.target;
        const formData = new FormData(form);
        const params = new URLSearchParams(formData);

        // Ensure tab parameter is set
        params.set('tab', 'registrations');

        // Redirect with filters
        window.location.href = '?' + params.toString();
        return false;
    }

    // Clear all filters and stay on registrations tab
    function clearFilters() {
        window.location.href = '?tab=registrations';
    }
    </script>
</head>
<body class="bg-gray-100">
    <div class="min-h-screen">
        <!-- Header -->
<header class="bg-white shadow-lg border-b border-gray-200">
            <div class="max-w-7xl mx-auto px-4 py-6 sm:px-6 lg:px-8">
                <div class="flex justify-between items-center mb-4">
                    <div class="flex items-center">
                        <div class="w-10 h-10 bg-red-600 rounded-full flex items-center justify-center mr-4">
                            <i class="fas fa-shield-alt text-white text-lg"></i>
                        </div>
                        <div>
                            <h1 class="text-2xl font-bold text-gray-900">Admin Dashboard</h1>
                            <p class="text-sm text-gray-600">Rhapsody Crusades Management</p>
                        </div>
                    </div>
                    
                    <div class="flex items-center space-x-4">
                        <?php $settings = getSettings(); $ipGuardEnabled = isset($settings['ip_guard_enabled']) ? (bool)$settings['ip_guard_enabled'] : true; ?>
                        <form method="post" action="" class="flex items-center space-x-2" onsubmit="return true;">
                            <input type="hidden" name="save_ip_guard" value="1" />
                            <label for="ip_guard_toggle" class="text-sm text-gray-700">IP Guard</label>
                            <button type="submit" name="ip_guard_enabled" value="<?php echo $ipGuardEnabled ? '0' : '1'; ?>" class="relative inline-flex h-6 w-11 items-center rounded-full <?php echo $ipGuardEnabled ? 'bg-green-600' : 'bg-gray-300'; ?>">
                                <span class="sr-only">Toggle IP Guard</span>
                                <span class="inline-block h-4 w-4 transform rounded-full bg-white transition <?php echo $ipGuardEnabled ? 'translate-x-6' : 'translate-x-1'; ?>"></span>
                            </button>
                            <span class="text-xs text-gray-600"><?php echo $ipGuardEnabled ? 'Enabled' : 'Disabled'; ?></span>
                        </form>
                        <!-- User Info -->
                        <div class="hidden md:flex items-center text-gray-700">
                            <div class="w-8 h-8 bg-gray-300 rounded-full flex items-center justify-center mr-2">
                                <i class="fas fa-user text-gray-600 text-sm"></i>
                            </div>
                            <span class="text-sm font-medium">Administrator</span>
                        </div>
                        
                        <!-- Logout Button -->
                        <a href="logout.php" 
                           class="inline-flex items-center px-4 py-2 text-sm font-medium text-white bg-red-600 hover:bg-red-700 transition-colors duration-200 shadow-sm border border-red-600 hover:border-red-700"
                           onclick="return confirm('Are you sure you want to logout?')">
                            <i class="fas fa-sign-out-alt mr-2"></i>
                            Logout
                        </a>
                        
                        <!-- Back to Site Link -->
                        <a href="../index.php" 
                           class="inline-flex items-center px-4 py-2 text-sm font-medium text-gray-700 bg-gray-100 hover:bg-gray-200 transition-colors duration-200 border border-gray-300">
                            <i class="fas fa-arrow-left mr-2"></i>
                            Back to Site
                        </a>
                    </div>
                </div>

                <!-- Tab Navigation -->
                <div class="border-b border-gray-200">
                    <nav class="-mb-px flex space-x-8">
                        <button onclick="switchTab('crusades')" class="tab-button <?php echo $currentTab === 'crusades' ? 'active' : ''; ?>">
                            <i class="fas fa-calendar-alt mr-2"></i>
                            Manage Crusades
                        </button>
                        <button onclick="switchTab('highlights')" class="tab-button <?php echo $currentTab === 'highlights' ? 'active' : ''; ?>">
                            <i class="fas fa-newspaper mr-2"></i>
                            R.E.C.C.O.R.D.s Highlights
                        </button>
                        <button onclick="switchTab('registrations')" class="tab-button <?php echo $currentTab === 'registrations' ? 'active' : ''; ?>">
                            <i class="fas fa-users mr-2"></i>
                            Registrations
                        </button>
                        <button onclick="switchTab('missions')" class="tab-button <?php echo $currentTab === 'missions' ? 'active' : ''; ?>">
                            <i class="fas fa-globe mr-2"></i>
                            Mission Trips
                        </button>
                        <button onclick="switchTab('media')" class="tab-button <?php echo $currentTab === 'media' ? 'active' : ''; ?>">
                            <i class="fas fa-video mr-2"></i>
                            Media
                        </button>
                        <button onclick="switchTab('world-requests')" class="tab-button <?php echo $currentTab === 'world-requests' ? 'active' : ''; ?>">
                            <i class="fas fa-globe-americas mr-2"></i>
                            World Requests
                        </button>
                    </nav>
                </div>
            </div>
        </header>

        <!-- Main Content -->
        <main class="max-w-7xl mx-auto px-4 py-6 sm:px-6 lg:px-8">
            <?php if ($message): ?>
                <div class="mb-6 p-4 rounded-md <?php echo $messageType === 'success' ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'; ?>">
                    <?php echo htmlspecialchars($message); ?>
                </div>
            <?php endif; ?>
            
            <?php 
            // Display session messages (for highlights and other operations)
            if (isset($_SESSION['message'])): 
            ?>
                <div class="mb-6 p-4 rounded-md <?php echo $_SESSION['messageType'] === 'success' ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'; ?>">
                    <?php 
                    echo htmlspecialchars($_SESSION['message']); 
                    unset($_SESSION['message'], $_SESSION['messageType']);
                    ?>
                </div>
            <?php endif; ?>

            <!-- Crusades Tab Content -->
            <div id="crusades-tab" class="tab-content <?php echo $currentTab === 'crusades' ? 'active' : ''; ?>">

            <!-- Add New Crusade Form -->
            <div class="bg-white rounded-lg shadow-md p-6 mb-8">
                <h2 class="text-xl font-semibold mb-4">Add New Crusade</h2>
                <form action="<?php echo htmlspecialchars($_SERVER['PHP_SELF']); ?>" method="POST" enctype="multipart/form-data" class="space-y-4" onsubmit="return validateForm()">
                    <input type="hidden" name="action" value="add_crusade">
                    
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div>
                            <label for="title" class="block text-sm font-medium text-gray-700">Title</label>
                            <input type="text" id="title" name="title" required 
                                   class="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-primary focus:border-primary">
                        </div>
                        
                        <div>
                            <label for="date" class="block text-sm font-medium text-gray-700">Date</label>
                            <input type="date" id="date" name="date" required 
                                   class="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-primary focus:border-primary">
                        </div>
                        
                        <div>
                            <label for="time" class="block text-sm font-medium text-gray-700">Time</label>
                            <input type="time" id="time" name="time" required 
                                   class="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-primary focus:border-primary">
                        </div>
                        
                        <div>
                            <label for="venue" class="block text-sm font-medium text-gray-700">Venue</label>
                            <input type="text" id="venue" name="venue" required 
                                   class="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-primary focus:border-primary">
                        </div>
                        
                        <div class="md:col-span-2">
                            <label for="address" class="block text-sm font-medium text-gray-700">(City, Country) Address</label>
                            <input type="text" id="address" name="address" required 
                                   class="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-primary focus:border-primary">
                        </div>
                        
                        <div class="md:col-span-2">
                            <label for="description" class="block text-sm font-medium text-gray-700">Description</label>
                            <textarea id="description" name="description" rows="3" required
                                     class="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-primary focus:border-primary"></textarea>
                        </div>
                        
                        <div>
                            <label for="register_link" class="block text-sm font-medium text-gray-700">Register Link</label>
                            <input type="text" id="register_link" name="register_link"
                                   class="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-primary focus:border-primary"
                                   placeholder="Leave blank to auto-generate from title">
                            <p class="mt-1 text-xs text-gray-500">Custom registration link (optional). If left blank, will auto-generate: register.php?event=title-slug</p>
                        </div>
                        
                        <div>
                            <label for="image" class="block text-sm font-medium text-gray-700">Event Image</label>
                            <input type="file" id="image" name="image" accept="image/*" 
                                   class="mt-1 block w-full text-sm text-gray-500 file:mr-4 file:py-2 file:px-4 file:rounded-md file:border-0 file:text-sm file:font-semibold file:bg-primary file:text-white hover:file:bg-primary-dark">
                            <p class="mt-1 text-xs text-gray-500">Leave empty to use default image</p>
                        </div>
                    </div>
                    
                    <div class="flex justify-end">
                        <button type="submit" class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-primary hover:bg-primary-dark focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary">
                            <i class="fas fa-plus mr-2"></i>
                            Add Crusade
                        </button>
                    </div>
                </form>
            </div>

            <!-- Current Crusades List -->
            <div class="bg-white rounded-lg shadow-md p-6">
                <h2 class="text-xl font-semibold mb-4">Current Crusades</h2>
                <?php if (empty($crusades['crusades'])): ?>
                    <p class="text-gray-500">No crusades found.</p>
                <?php else: ?>
                    <div class="overflow-x-auto">
                        <table class="min-w-full divide-y divide-gray-200">
                            <thead class="bg-gray-50">
                                <tr>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Image</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Title</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Date & Time</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Venue</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                                </tr>
                            </thead>
                            <tbody class="bg-white divide-y divide-gray-200">
                                <?php foreach ($crusades['crusades'] as $crusade): 
                                    $eventDate = new DateTime($crusade['date'] . ' ' . $crusade['time']);
                                    $isPast = $eventDate < new DateTime();
                                ?>
                                    <tr class="<?php echo $isPast ? 'bg-gray-50' : ''; ?>">
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <div class="h-10 w-10 rounded-full overflow-hidden">
                                                <img src="../<?php echo htmlspecialchars($crusade['image']); ?>" alt="" class="h-full w-full object-cover">
                                            </div>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <div class="text-sm font-medium text-gray-900"><?php echo htmlspecialchars($crusade['title']); ?></div>
                                            <div class="text-sm text-gray-500 truncate max-w-xs"><?php echo htmlspecialchars($crusade['description']); ?></div>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <div class="text-sm text-gray-900"><?php echo $eventDate->format('M j, Y'); ?></div>
                                            <div class="text-sm text-gray-500"><?php echo $eventDate->format('g:i A'); ?></div>
                                            <?php if ($isPast): ?>
                                                <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-red-100 text-red-800">Past Event</span>
                                            <?php else: ?>
                                                <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 text-green-800">Upcoming</span>
                                            <?php endif; ?>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <div class="text-sm text-gray-900"><?php echo htmlspecialchars($crusade['venue']); ?></div>
                                            <div class="text-sm text-gray-500"><?php echo htmlspecialchars($crusade['address']); ?></div>
                                            <div class="mt-2">
                                                <?php 
                                                $registerLink = !empty($crusade['register_link']) ? $crusade['register_link'] : '';
                                                if (!empty($registerLink)) {
                                                    // If it's a full URL, use it as is, otherwise prepend the base path
                                                    if (strpos($registerLink, 'http') === 0) {
                                                        $regUrl = $registerLink;
                                                    } else {
                                                        $regUrl = '../' . ltrim($registerLink, '/');
                                                    }
                                                    ?>
                                                    <a href="<?php echo htmlspecialchars($regUrl); ?>" target="_blank" class="text-xs text-blue-600 hover:text-blue-800 hover:underline">
                                                        View Registration Page
                                                    </a>
                                                <?php } ?>
                                            </div>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <div class="flex items-center justify-end space-x-4">
                                                <a href="edit_crusade.php?id=<?php echo $crusade['id']; ?>" class="inline-flex items-center px-3 py-1.5 border border-transparent text-xs font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors">
                                                    <i class="fas fa-edit mr-1.5"></i> Edit
                                                </a>
                                                <form method="POST" action="" class="inline-block" onsubmit="return confirm('Are you sure you want to delete this crusade?');">
                                                    <input type="hidden" name="action" value="delete">
                                                    <input type="hidden" name="crusade_id" value="<?php echo $crusade['id']; ?>">
                                                    <button type="submit" class="inline-flex items-center px-3 py-1.5 border border-transparent text-xs font-medium rounded-md text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 transition-colors">
                                                        <i class="fas fa-trash mr-1.5"></i> Delete
                                                    </button>
                                                </form>
                                            </div>
                                        </td>
                                    </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                <?php endif; ?>
            </div>
            </div>

            <!-- Highlights Tab Content -->
            <div id="highlights-tab" class="tab-content <?php echo $currentTab === 'highlights' ? 'active' : ''; ?>">
                <?php include 'highlights_dashboard.php'; ?>
            </div>

            <!-- Registrations Tab Content -->
            <div id="registrations-tab" class="tab-content <?php echo $currentTab === 'registrations' ? 'active' : ''; ?>">
                <?php include 'registrations_dashboard.php'; ?>
            </div>

            <!-- Mission Trips Tab Content -->
            <div id="missions-tab" class="tab-content <?php echo $currentTab === 'missions' ? 'active' : ''; ?>">
                <?php include 'mission_trips_dashboard.php'; ?>
            </div>

            <!-- Media Tab Content -->
            <div id="media-tab" class="tab-content <?php echo $currentTab === 'media' ? 'active' : ''; ?>">
                <?php 
                if (file_exists('media_dashboard.php')) {
                    include 'media_dashboard.php'; 
                } else {
                    echo '<div class="p-4 bg-red-100 text-red-700 rounded">Error: media_dashboard.php not found</div>';
                }
                ?>
            </div>

            <!-- World Requests Tab Content -->
            <div id="world-requests-tab" class="tab-content <?php echo $currentTab === 'world-requests' ? 'active' : ''; ?>">
                <?php 
                if (file_exists('world_crusade_requests_dashboard.php')) {
                    include 'world_crusade_requests_dashboard.php'; 
                } else {
                    echo '<div class="p-4 bg-red-100 text-red-700 rounded">Error: world_crusade_requests_dashboard.php not found</div>';
                }
                ?>
            </div>
        </main>
    </div>
</body>
</html>
